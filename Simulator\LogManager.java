package Simulator;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * GESTIONNAIRE DE JOURNALISATION POUR LA SIMULATION
 *
 * Cette classe s'occupe de tous les messages qui s'affichent pendant la simulation.
 * Elle utilise le pattern Singleton, ce qui veut dire qu'il ne peut y avoir qu'une seule
 * instance de cette classe dans tout le programme. C'est comme avoir un seul carnet
 * de notes pour toute la simulation.
 *
 * Fonctionnalités principales :
 * - Afficher des messages avec des couleurs différentes selon le type
 * - Compter combien de messages de chaque type ont été envoyés
 * - Ajouter l'heure à chaque message pour savoir quand ça s'est passé
 * - Permettre d'activer ou désactiver l'affichage des messages
 */
public class LogManager {

    // Variable qui contient la seule instance de cette classe (pattern Singleton)
    private static LogManager instanceUnique;

    // Codes spéciaux pour afficher des couleurs dans la console
    // Ces codes sont reconnus par la plupart des terminaux pour changer la couleur du texte
    public static final String COULEUR_RESET = "\u001B[0m";      // Remet la couleur normale
    public static final String COULEUR_VERT = "\u001B[32m";      // Texte vert
    public static final String COULEUR_JAUNE = "\u001B[33m";     // Texte jaune
    public static final String COULEUR_BLEU = "\u001B[34m";      // Texte bleu
    public static final String COULEUR_CYAN = "\u001B[36m";      // Texte cyan
    public static final String COULEUR_VIOLET = "\u001B[35m";    // Texte violet
    public static final String COULEUR_ROUGE = "\u001B[31m";     // Texte rouge
    public static final String TEXTE_GRAS = "\u001B[1m";         // Texte en gras

    /**
     * TYPES DE MESSAGES AVEC LEURS COULEURS
     *
     * Cette énumération définit tous les types de messages possibles dans la simulation.
     * Chaque type a un nom et une couleur associée pour faciliter la lecture des logs.
     *
     * Les types disponibles :
     * - ACTION : pour les actions générales des robots (vert)
     * - BATTERY : pour tout ce qui concerne la batterie (jaune)
     * - TRANSIT : pour les déplacements entre zones (bleu)
     * - COORDINATION : pour la communication entre robots (cyan)
     * - DELIVERY : pour la livraison de colis (violet)
     * - ERROR : pour les erreurs (rouge)
     */
    public enum TypeDeMessage {
        ACTION(COULEUR_VERT, "ACTION"),
        BATTERY(COULEUR_JAUNE, "BATTERIE"),
        TRANSIT(COULEUR_BLEU, "TRANSIT"),
        COORDINATION(COULEUR_CYAN, "COORDINATION"),
        DELIVERY(COULEUR_VIOLET, "LIVRAISON"),
        ERROR(COULEUR_ROUGE, "ERREUR");

        // Variables pour stocker la couleur et le nom de chaque type
        private final String couleurDuType;
        private final String nomDuType;

        /**
         * Constructeur pour créer un type de message
         * @param couleur Le code couleur ANSI (ex: "\u001B[32m")
         * @param nom Le nom du type (ex: "ACTION")
         */
        TypeDeMessage(String couleur, String nom) {
            this.couleurDuType = couleur;
            this.nomDuType = nom;
        }

        /**
         * Récupère la couleur associée au type de message
         * @return Le code couleur ANSI
         */
        public String getCouleur() {
            return couleurDuType;
        }

        /**
         * Récupère le nom du type de message
         * @return Le nom du type (ex: "ACTION")
         */
        public String getNom() {
            return nomDuType;
        }
    }

    // Variables de configuration pour personnaliser le comportement du gestionnaire
    private boolean modeVerbeux = true;           // Si true, affiche tous les messages
    private boolean utiliserCouleurs = true;     // Si true, utilise les couleurs dans les messages
    private boolean ecrireDansFichier = false;   // Si true, sauvegarde aussi dans un fichier
    private String cheminFichierLog = "simulation.log";  // Nom du fichier de sauvegarde

    // Variables pour compter les statistiques
    // Map qui associe chaque robot à ses statistiques de messages par type
    private Map<String, Map<TypeDeMessage, AtomicInteger>> statistiquesParRobot = new ConcurrentHashMap<>();
    // Map qui compte le total global de chaque type de message
    private Map<TypeDeMessage, AtomicInteger> statistiquesGlobales = new ConcurrentHashMap<>();

    // Objet pour formater la date et l'heure dans les messages
    private SimpleDateFormat formateurDate = new SimpleDateFormat("HH:mm:ss.SSS");

    /**
     * CONSTRUCTEUR PRIVÉ (PATTERN SINGLETON)
     *
     * Ce constructeur est privé, ce qui signifie qu'on ne peut pas créer directement
     * une instance de LogManager avec "new LogManager()". Il faut passer par la méthode
     * getInstance() pour obtenir l'instance unique.
     *
     * Le constructeur initialise :
     * - Les compteurs de statistiques pour chaque type de message
     * - Affiche un message pour confirmer que le système est prêt
     */
    private LogManager() {
        // Initialiser les compteurs de statistiques globales
        // On parcourt tous les types de messages définis dans l'énumération
        for (TypeDeMessage type : TypeDeMessage.values()) {
            // Pour chaque type, on crée un compteur qui commence à 0
            statistiquesGlobales.put(type, new AtomicInteger(0));
        }

        // Afficher un message de confirmation que le système est initialisé
        System.out.println(TEXTE_GRAS + "Système de journalisation initialisé et prêt" + COULEUR_RESET);
    }

    /**
     * MÉTHODE POUR OBTENIR L'INSTANCE UNIQUE (PATTERN SINGLETON)
     *
     * Cette méthode est le seul moyen d'obtenir une instance de LogManager.
     * Elle vérifie si une instance existe déjà :
     * - Si oui, elle la retourne
     * - Si non, elle en crée une nouvelle
     *
     * Le mot-clé "synchronized" assure que même si plusieurs threads appellent
     * cette méthode en même temps, il n'y aura qu'une seule instance créée.
     *
     * @return L'instance unique de LogManager
     */
    public static synchronized LogManager getInstance() {
        // Vérifier si l'instance n'existe pas encore
        if (instanceUnique == null) {
            // Créer la première et unique instance
            instanceUnique = new LogManager();
        }
        // Retourner l'instance (nouvelle ou existante)
        return instanceUnique;
    }

    /**
     * MÉTHODE POUR CONFIGURER LE MODE VERBEUX
     *
     * Cette méthode permet d'activer ou désactiver l'affichage de tous les messages.
     * Quand le mode verbeux est désactivé, seules les erreurs sont affichées.
     *
     * @param verbeux true pour afficher tous les messages, false pour n'afficher que les erreurs
     */
    public void setVerboseMode(boolean verbeux) {
        this.modeVerbeux = verbeux;
    }

    /**
     * MÉTHODE POUR CONFIGURER L'UTILISATION DES COULEURS
     *
     * Cette méthode permet d'activer ou désactiver l'affichage des couleurs dans les messages.
     * Utile si le terminal ne supporte pas les couleurs ANSI.
     *
     * @param utiliserCouleurs true pour utiliser les couleurs, false pour du texte simple
     */
    public void setUseColors(boolean utiliserCouleurs) {
        this.utiliserCouleurs = utiliserCouleurs;
    }

    /**
     * MÉTHODE POUR CONFIGURER L'ENREGISTREMENT DANS UN FICHIER
     *
     * Cette méthode permet d'activer la sauvegarde des messages dans un fichier.
     * Actuellement non implémentée (TODO).
     *
     * @param ecrireFichier true pour sauvegarder dans un fichier
     * @param cheminFichier le chemin du fichier de sauvegarde (peut être null)
     */
    public void setLogToFile(boolean ecrireFichier, String cheminFichier) {
        this.ecrireDansFichier = ecrireFichier;
        if (cheminFichier != null) {
            this.cheminFichierLog = cheminFichier;
        }
    }

    /**
     * MÉTHODE PRINCIPALE POUR ENREGISTRER UN MESSAGE
     *
     * Cette méthode est le cœur du système de journalisation. Elle :
     * 1. Met à jour les statistiques globales et par robot
     * 2. Vérifie si le message doit être affiché selon le mode verbeux
     * 3. Formate le message avec l'heure, le type et le nom du robot
     * 4. Ajoute les couleurs si activées
     * 5. Affiche le message dans la console
     *
     * @param nomRobot Le nom du robot qui envoie le message
     * @param typeMessage Le type de message (ACTION, BATTERY, etc.)
     * @param contenuMessage Le contenu du message à afficher
     */
    private void enregistrerMessage(String nomRobot, TypeDeMessage typeMessage, String contenuMessage) {
        // Étape 1 : Incrémenter les statistiques globales
        // On augmente de 1 le compteur pour ce type de message
        statistiquesGlobales.get(typeMessage).incrementAndGet();

        // Étape 2 : Mettre à jour les statistiques par robot
        // Si c'est la première fois qu'on voit ce robot, on crée ses compteurs
        statistiquesParRobot.computeIfAbsent(nomRobot, nomDuRobot -> {
            // Créer une nouvelle map pour ce robot avec tous les types de messages
            Map<TypeDeMessage, AtomicInteger> statistiquesDuRobot = new ConcurrentHashMap<>();
            for (TypeDeMessage type : TypeDeMessage.values()) {
                // Initialiser chaque compteur à 0
                statistiquesDuRobot.put(type, new AtomicInteger(0));
            }
            return statistiquesDuRobot;
        }).get(typeMessage).incrementAndGet(); // Puis on incrémente le compteur pour ce type

        // Étape 3 : Vérifier si on doit afficher le message
        // Si le mode verbeux est désactivé ET que ce n'est pas une erreur, on n'affiche pas
        if (!modeVerbeux && typeMessage != TypeDeMessage.ERROR) {
            return; // Sortir de la méthode sans afficher
        }

        // Étape 4 : Formater le message avec l'heure
        // Créer un timestamp avec l'heure actuelle
        String heureActuelle = formateurDate.format(new Date());
        // Construire le message final avec le format : [heure] [type] [robot] message
        String messageFinal = String.format("[%s] [%s] [%s] %s",
                heureActuelle,
                typeMessage.getNom(),
                nomRobot,
                contenuMessage);

        // Étape 5 : Ajouter les couleurs si activées
        if (utiliserCouleurs) {
            // Entourer le message avec les codes couleur ANSI
            messageFinal = typeMessage.getCouleur() + messageFinal + COULEUR_RESET;
        }

        // Étape 6 : Afficher le message dans la console
        System.out.println(messageFinal);

        // TODO: Enregistrer dans un fichier si activé
        // Cette fonctionnalité pourrait être ajoutée plus tard
    }

    /**
     * MÉTHODE POUR ENREGISTRER LES ACTIONS GÉNÉRALES DES ROBOTS
     *
     * Cette méthode est utilisée pour enregistrer toutes les actions normales que font les robots,
     * comme se déplacer, prendre un colis, changer de direction, etc.
     * Les messages d'action sont affichés en vert pour les distinguer facilement.
     *
     * @param nomRobot Le nom du robot qui effectue l'action
     * @param messageAction Description de l'action effectuée
     */
    public void logAction(String nomRobot, String messageAction) {
        enregistrerMessage(nomRobot, TypeDeMessage.ACTION, messageAction);
    }

    /**
     * MÉTHODE POUR ENREGISTRER LES ÉVÉNEMENTS LIÉS À LA BATTERIE
     *
     * Cette méthode est spécialement conçue pour les messages concernant la batterie des robots.
     * Elle formate automatiquement le niveau de batterie en pourcentage et ajoute le message.
     * Les messages de batterie sont affichés en jaune pour attirer l'attention.
     *
     * @param nomRobot Le nom du robot concerné
     * @param niveauBatterie Le niveau actuel de la batterie (entre 0 et 100)
     * @param messageDetail Message détaillant l'événement de batterie
     */
    public void logBattery(String nomRobot, double niveauBatterie, String messageDetail) {
        // Formater le message avec le niveau de batterie en pourcentage
        String messageComplet = String.format("Niveau: %.1f%% - %s", niveauBatterie, messageDetail);
        enregistrerMessage(nomRobot, TypeDeMessage.BATTERY, messageComplet);
    }

    /**
     * MÉTHODE POUR ENREGISTRER LES MOUVEMENTS ENTRE ZONES
     *
     * Cette méthode est utilisée pour tracer les déplacements des robots entre différentes zones
     * (zones de départ, zones de transit, zones d'arrivée). Elle aide à comprendre le parcours
     * des robots dans l'entrepôt. Les messages de transit sont affichés en bleu.
     *
     * @param nomRobot Le nom du robot qui se déplace
     * @param infoZone Information sur la zone (coordonnées, nom, etc.)
     * @param messageTransit Description du mouvement ou de l'événement de transit
     */
    public void logTransit(String nomRobot, String infoZone, String messageTransit) {
        // Combiner l'information de zone avec le message de transit
        String messageComplet = String.format("%s - %s", infoZone, messageTransit);
        enregistrerMessage(nomRobot, TypeDeMessage.TRANSIT, messageComplet);
    }

    /**
     * MÉTHODE POUR ENREGISTRER LA COMMUNICATION ENTRE ROBOTS
     *
     * Cette méthode est utilisée pour tracer tous les échanges de messages entre robots,
     * comme les enchères, les négociations, les annonces de tâches, etc.
     * Les messages de coordination sont affichés en cyan pour les identifier facilement.
     *
     * @param nomRobot Le nom du robot qui envoie ou reçoit le message
     * @param messageCoordination Description de la communication ou de la coordination
     */
    public void logCoordination(String nomRobot, String messageCoordination) {
        enregistrerMessage(nomRobot, TypeDeMessage.COORDINATION, messageCoordination);
    }

    /**
     * MÉTHODE POUR ENREGISTRER LA PRISE ET LE DÉPÔT DE COLIS
     *
     * Cette méthode trace toutes les opérations de livraison : prise de colis, transport,
     * dépôt dans les zones de transit ou d'arrivée. Elle formate automatiquement l'information
     * du colis. Les messages de livraison sont affichés en violet.
     *
     * @param nomRobot Le nom du robot qui manipule le colis
     * @param infoColis Information sur le colis (ID, destination, etc.)
     * @param messageLivraison Description de l'opération de livraison
     */
    public void logDelivery(String nomRobot, String infoColis, String messageLivraison) {
        // Formater le message avec l'information du colis
        String messageComplet = String.format("Colis %s - %s", infoColis, messageLivraison);
        enregistrerMessage(nomRobot, TypeDeMessage.DELIVERY, messageComplet);
    }

    /**
     * MÉTHODE POUR ENREGISTRER LES ÉVÉNEMENTS DE CHARGE DE BATTERIE
     *
     * Cette méthode est spécialement conçue pour tracer les sessions de recharge des robots.
     * Elle affiche clairement l'évolution du niveau de batterie avec une flèche.
     * Ces messages utilisent le type BATTERY et sont donc affichés en jaune.
     *
     * @param nomRobot Le nom du robot qui se recharge
     * @param niveauAvant Le niveau de batterie avant la recharge
     * @param niveauApres Le niveau de batterie après la recharge
     * @param messageCharge Description de l'événement de charge
     */
    public void logCharging(String nomRobot, double niveauAvant, double niveauApres, String messageCharge) {
        // Formater le message avec l'évolution de la batterie
        String messageComplet = String.format("Charge: %.1f%% → %.1f%% - %s", niveauAvant, niveauApres, messageCharge);
        enregistrerMessage(nomRobot, TypeDeMessage.BATTERY, messageComplet);
    }

    /**
     * MÉTHODE POUR ENREGISTRER LES ERREURS ET EXCEPTIONS
     *
     * Cette méthode est utilisée pour tous les problèmes, erreurs et situations exceptionnelles.
     * Les messages d'erreur sont toujours affichés (même en mode non verbeux) et apparaissent
     * en rouge pour attirer immédiatement l'attention.
     *
     * @param nomRobot Le nom du robot ou composant qui a rencontré l'erreur
     * @param messageErreur Description de l'erreur ou du problème
     */
    public void logError(String nomRobot, String messageErreur) {
        enregistrerMessage(nomRobot, TypeDeMessage.ERROR, messageErreur);
    }

    /**
     * MÉTHODE POUR AFFICHER LES STATISTIQUES DES MESSAGES
     *
     * Cette méthode affiche un résumé complet de tous les messages qui ont été envoyés
     * pendant la simulation. Elle montre :
     * 1. Les statistiques globales : combien de messages de chaque type au total
     * 2. Les statistiques par robot : combien de messages chaque robot a envoyé par type
     *
     * C'est très utile pour analyser le comportement de la simulation et voir
     * quels robots sont les plus actifs ou quels types d'événements sont les plus fréquents.
     */
    public void printStatistics() {
        // Afficher le titre principal en gras
        System.out.println("\n" + TEXTE_GRAS + "=== STATISTIQUES DES MESSAGES ===" + COULEUR_RESET);

        // Section 1 : Statistiques globales (tous robots confondus)
        System.out.println("\nStatistiques globales:");
        // Parcourir tous les types de messages définis
        for (TypeDeMessage type : TypeDeMessage.values()) {
            // Déterminer la couleur à utiliser selon les préférences
            String couleur = utiliserCouleurs ? type.getCouleur() : "";
            String reset = utiliserCouleurs ? COULEUR_RESET : "";
            // Afficher le type de message avec sa couleur et le nombre total
            System.out.printf("%s%-12s%s: %d messages\n",
                    couleur,
                    type.getNom(),
                    reset,
                    statistiquesGlobales.get(type).get());
        }

        // Section 2 : Statistiques détaillées par robot
        System.out.println("\nStatistiques par robot:");
        // Parcourir tous les robots qui ont envoyé des messages
        for (Map.Entry<String, Map<TypeDeMessage, AtomicInteger>> entree : statistiquesParRobot.entrySet()) {
            String nomRobot = entree.getKey();
            Map<TypeDeMessage, AtomicInteger> statistiquesDuRobot = entree.getValue();

            // Afficher le nom du robot en gras
            System.out.println("\n" + TEXTE_GRAS + nomRobot + COULEUR_RESET + ":");
            // Afficher les statistiques de ce robot pour chaque type de message
            for (TypeDeMessage type : TypeDeMessage.values()) {
                // Déterminer la couleur à utiliser
                String couleur = utiliserCouleurs ? type.getCouleur() : "";
                String reset = utiliserCouleurs ? COULEUR_RESET : "";
                // Afficher avec une indentation pour montrer que c'est un sous-élément
                System.out.printf("  %s%-12s%s: %d messages\n",
                        couleur,
                        type.getNom(),
                        reset,
                        statistiquesDuRobot.get(type).get());
            }
        }
    }
}
