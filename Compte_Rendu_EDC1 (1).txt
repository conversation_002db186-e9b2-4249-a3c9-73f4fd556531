Compte-rendu Étude de Cas 1
AKAYAD Noa
Mines Saint-Étienne
4 mai 2025
Étude de cas sur la prédiction des marées dans le port de l’île d’Yeu
à partir de données expérimentales issues d’un marégraphe Arduino.

1

Extraction des données expérimentales

Premièrement, il a fallu extraire les données de tous les fichiers fournis. Pour cela, j’ai utilisé
la même logique que celle vue dans les travaux pratiques, notamment le TP 4. En effet, j’ai
mis en place une macro contenant une fonction async run, utilisant les mêmes fonctions
fetchCSV et resolve que celles du TP4.
J’ai commencé par extraire tous les liens des fichiers de données grâce à cette méthode, en les
stockant dans un tableau nommé liens. Par la suite, il suffisait de répéter la même méthode
pour chaque lien afin d’accéder à son contenu.
Initialement, j’ai tenté une approche brute consistant à importer tous les fichiers d’un seul
coup. Cependant, cette méthode a posé de gros problèmes :
— Le nombre de lignes total (plus de 80 000) provoquait des crashs lors de l’exécution,
empêchant toute extraction. Il semble que la fonction GetRangeByNumber ne supportait
pas un tel volume.
— Cette approche n’était pas adaptée à l’étape suivante du TP qui demandait de filtrer
les données : certaines étant aberrantes.
J’ai donc décidé d’extraire chaque fichier un par un. Comme le nombre de fichiers était
connu, il suffisait dans ma macro d’accéder à chacun d’eux via let lien = liens[i] pour
obtenir le lien i-ème, puis d’extraire son contenu comme dans le TP4.
Une fois un fichier importé, je le filtrai manuellement en parcourant chaque ligne. Ma réflexion

Compte-rendu EDC1

2

était la suivante :
— Si une valeur à une ligne donnée était aberrante (par exemple hauteur < 100), je
supprimais la ligne.
— Si une valeur était trop différente de ses voisines (lignes supérieures et inférieures), je
la supprimais aussi.
Certains fichiers étaient trop volumineux pour être importés directement. J’ai donc dû les
diviser en plusieurs portions (parfois jusqu’à 11), que j’importais et filtrais séparément.
Cette manipulation m’a permis d’obtenir exactement 80 658 lignes de données filtrées. Cidessous un extrait illustratif du code utilisé pour réaliser cette extraction :

1

async function fetchCSV ( uri , delimiter = ’\t ’) {

2

let response = await fetch ( uri ) ;

3

let txt = await response . text () ;

4

return txt

5

. split ( ’\n ’)

6

. filter ( function ( l ) { return l ; })

7

. map ( function ( l ) { return l . split ( delimiter ) ; }) ;

8

}

9
10

function resolve ( uri , path ) {

11

let i = uri . lastIndexOf ("/") ;

12

return uri . slice (0 , i + 1) + path ;

13

}

14
15

let liens = data . split ( ’\n ’) ;

16

let lien = liens [32];

17

let url = resolve ( uri , lien ) ;

18

let data_lien = await fetchCSV ( url ) ;

19
20

// Division du fichier en portions plus petites

21

let portionSize = Math . floor ( data_lien . length / 11) ;

22

...

Listing 1 – Extrait de la macro d’importation JavaScript

Compte-rendu EDC1

2

3

Calcul des coefficients harmoniques et validation du modèle

Une fois les données collectées, il a fallu les exploiter pour obtenir, grâce au code MATLAB,
les coefficients harmoniques. Comme expliqué précédemment, un premier filtrage manuel
avait déjà été réalisé au moment de l’importation des fichiers. Dans une feuille Excel intitulée
Données, j’ai donc importé toutes les données de type 1 (avec hauteur unique) telles quelles,
et j’ai calculé la moyenne des 10 mesures de hauteur pour les données de type 2.
Ces données ont été mises au format attendu dans un fichier Ye.txt pour pouvoir être exploitées dans le code MATLAB de prédiction des marées. Après plusieurs tentatives infructueuses
(erreurs de tabulations, de dates ou de délimiteurs), j’ai enfin obtenu une première série de
coefficients : la constante z0 , ainsi que les couples (Ai , gi ). Cependant, en comparant mes valeurs avec celles de camarades de classe, il m’a semblé que mes résultats étaient incohérents,
ce qui m’a conduit à remettre en question la fiabilité du premier filtrage manuel.
J’ai donc mis en place un second filtrage, cette fois automatisé dans une macro. Celle-ci
applique les mêmes critères que mon filtrage manuel, mais de manière systématique sur
toutes les données importées et en gardant une distance de 50cm entre les hauteurs plutôt
que 100.
1

if ( temp > -5 && temp < 40 &&

2

pression > 950 && pression < 1050 &&

3

distance > 100 && distance < 700) {

4

...

5

}

6
7

// Suppression des lignes trop diff \ ’ erentes de leurs voisines

8

if ( Math . abs ( distance [ i ] - distance [ i +1]) <= 50) {
filtre . push (...) ;

9
10

}

Listing 2 – Filtrage automatique des données brutes
Ces critères permettent de conserver uniquement les mesures avec des conditions physiques
plausibles (température raisonnable, pression réaliste et distances cohérentes). Ce filtrage
automatique a réduit le jeu de données de 80 000 à environ 78 000 lignes, tout en assurant
une bien meilleure qualité.
En utilisant ce nouveau fichier Ye.txt, j’ai obtenu les coefficients suivants :

Compte-rendu EDC1

4
Z0

Ai

gi

333.3

162.32

73.50

58.16

-56.51

34.85

191.82

5.41

255.19

0.19165 -27.973
0.26825 -1.9903

...

7.0478

229.45

5.9038

78.666

0.18236

27.187

5.9785

194.31

5.1531

-49.101

0.1183

257.24

...

...

Ces coefficients semblaient bien plus cohérents. Afin d’en avoir le cœur net, j’ai mené une
vérification plus poussée en réimplémentant le modèle de prédiction des hauteurs d’eau.
Le plus gros obstacle à ce stade était la conversion des dates en heures décimales depuis le
1/1/1900, essentielle pour le calcul de la formule :
1

let t_ = function ( dateStr ) {

2

let parts = dateStr . split ( ’ ’) ;

3

let dateParts = parts [0]. split ( ’/ ’) ;

4

let timeParts = parts [1]. split ( ’: ’) ;

5

...

6

let totalJours = jours + fractionDuJour ;

7

return totalJours * 24;

8

};

Listing 3 – Conversion date vers heure décimale

Pour que la formule de prédiction fonctionne correctement, il fallait convertir les dates en
une valeur numérique représentant le nombre d’heures écoulées depuis le 1 janvier 1900 à
minuit. Pour cela, j’ai écrit une fonction JavaScript que j’ai ap. Elle prend une date sous
forme de texte et la transforme en une heure décimale.
L’idée de cette fonction est assez simple : je commence par séparer la date et l’heure, puis
je transforme les éléments (jour, mois, année, heure, minute, seconde) en entiers. Ensuite, je
calcule combien de jours se sont écoulés depuis le 1 janvier 1900 jusqu’à la veille de l’année

Compte-rendu EDC1

5

concernée, en tenant compte des années bissextiles. Une fois l’année traitée, j’ajoute les jours
des mois précédents, puis le jour en cours. Enfin, j’ajoute l’heure exprimée en fraction de
journée, et je multiplie le tout par 24 pour avoir une valeur en heures.
Ce calcul peut sembler un peu long, mais il était nécessaire pour pouvoir évaluer précisément
la formule harmonique du modèle, qui dépend de l’heure décimale.
J’ai ensuite pu appliquer la formule du modèle de marée pour prédire les hauteurs :

h(t) = z0 +

n
X

fi Ai cos(ωi t + (v0 + u)i − gi )

i=1

Pour chaque date des données filtrées, j’ai ainsi comparé la hauteur réelle et la hauteur
prédite. Voici un extrait de mes résultats :
Date

Hauteur

Temp.

Pression

Hauteur Prédite

10/04/2012 16 :42 :10

168.9

12.88

999.138

170.82

10/04/2012 16 :48 :10

172.5

12.80

999.217

166.40

10/04/2012 16 :54 :10

161.8

12.70

999.238

162.39

10/04/2012 17 :00 :10

159.0

12.80

999.391

158.77

10/04/2012 17 :06 :10

153.1

12.41

999.448

155.55

10/04/2012 17 :12 :10

153.8

12.00

999.520

152.70

10/04/2012 17 :18 :10

144.2

12.00

999.547

150.23

10/04/2012 17 :24 :10

146.8

12.00

999.679

148.12

10/04/2012 17 :30 :10

141.2

12.24

999.744

146.37

Pour quantifier la précision du modèle, j’ai calculé deux indicateurs :
— l’erreur absolue moyenne (MAE) : 10,84 cm,
— l’erreur relative moyenne : 3,25%.
Ces résultats sont très satisfaisants au vu des hauteurs d’eau moyennes (autour de 500 cm).
Le MAE de 10 cm représente seulement 2% du niveau moyen, ce qui confirme que le modèle
est très proche des observations.
Enfin, j’ai réalisé deux graphiques de comparaison entre les hauteurs réelles et les hauteurs
prédites :

Ces deux représentations montrent que le modèle suit bien les oscillations naturelles des
marées. Les prédictions sont globalement très proches des mesures réelles. Quelques incerti-

Compte-rendu EDC1

6

Figure 1 – Comparaison des hauteurs réelles et prédites du 10/04 au 22/04/2012

Figure 2 – Comparaison pour la journée du 01/08/2012
tudes subsistent (pics trop lissés, légers décalages temporels), mais elles restent faibles et ne
remettent pas en question la qualité générale du prédicteur.
Conclusion les coefficients calculés avec filtrage automatisé permettent d’obtenir un modèle
de prédiction des marées plutôt fiable, cohérent et assez conforme à l’attendu.

3

Formulaire de prédiction des marées

Dans cette dernière partie, il fallait exploiter le prédicteur mis en place pour créer un formulaire. Celui-ci, à partir d’une date donnée, permet d’afficher les horaires des basses mers
(BM) et des pleines mers (PM) sur les 24 heures suivantes, dans l’ordre d’occurrence.
Pour cela, j’ai mis en place une macro qui, en fonction d’une date saisie, renvoie :
— les heures précises des deux basses mers et des deux pleines mers suivant cette date ;

Compte-rendu EDC1

7

— un tableau contenant toutes les hauteurs prédites durant les 24 heures suivant la date
donnée (avec un pas de 6 minutes) ;
— un graphique illustrant ces hauteurs, permettant de visualiser les variations et donc de
repérer visuellement les minima et maxima.
Mon raisonnement s’est fondé sur une observation simple : lorsque l’on regarde un graphe
de hauteur d’eau pour une journée donnée, deux schémas possibles apparaissent toujours :
— soit on a une alternance BM1, PM1, BM2, PM2 ;
— soit on a l’inverse : PM1, BM1, PM2, BM2.
Ainsi, l’ordre d’apparition des marées se déduit du comportement initial de la courbe de
hauteur :
— si la fonction hauteur est décroissante au début (h[0] > h[1]), alors on commence
par une basse mer ;
— sinon, on commence par une pleine mer.
Pour identifier les horaires des marées, j’ai ensuite simplement analysé les variations successives du tableau des hauteurs. Dès qu’une croissance devient une décroissance (ou inversement), on en déduit une pleine ou une basse mer selon le cas. J’ai répété ce raisonnement
pour trouver les quatre valeurs cherchées.
Pour cela j’ai principalement utilisé 2 fonctions, ma fonction t_ qui convertit une date en
heure décimale, et la fonction Dates :
1

let joursParMois = [31 , 28 , 31 , 30 , ... , 31];

2
3

let estBissextile = function ( a ) {
return ( a % 4 === 0 && a % 100 !== 0) || ( a % 400 === 0) ;

4
5

};

6
7

let joursDansMois = function (m , a ) {
if ( m === 2 && estBissextile ( a ) ) return 29;

8

return joursParMois [ m - 1];

9
10

};

11
12
13

for ( let i = 0; i <= 240; i ++) {
let totalSecondes = heure * 3600 + minute * 60 + seconde + i * 6 *
60;

14
15

let newSeconde = totalSecondes % 60;

16

let totalMinutes = Math . floor ( totalSecondes / 60) ;

17

let newMinute = totalMinutes % 60;

18

let totalHeures = Math . floor ( totalMinutes / 60) ;

Compte-rendu EDC1

8

19

let newHeure = totalHeures % 24;

20

let ajoutJours = Math . floor ( totalHeures / 24) ;

21
22

// Calcul du jour , mois , a n n e

23

...

a p r s ajout

24
25

let dateTexte = ...; // formatage final " jj / mm / aaaa hh : mm : ss "

26

dates . push ( dateTexte ) ;

27

}

Listing 4 – Extrait de la fonction Dates

Afin de prédire les basses mers et pleines mers à partir d’une date donnée, j’ai eu besoin
de générer toutes les dates comprises dans les 24 heures suivantes, espacées régulièrement
toutes les 6 minutes. Pour cela, j’ai écrit une fonction que j’ai appelée Dates.
L’idée de cette fonction est la suivante : à partir d’une date donnée sous forme de texte
(comme "10/04/2012 16 :42 :10"), je commence par la découper pour séparer la date et
l’heure. Ensuite, je transforme chaque élément (jour, mois, année, heure, minute, seconde)
en entier pour pouvoir faire des calculs.
Une fois que j’ai ces éléments, je crée une boucle qui tourne 241 fois (car 24 heures divisées
par 6 minutes donne 240 intervalles, donc 241 points), et à chaque tour je calcule l’heure
exacte en ajoutant 6 minutes supplémentaires au temps de départ.
Ce qui est un peu plus compliqué, c’est qu’en ajoutant 6 minutes à chaque fois, on peut
très vite changer d’heure, de jour, voire de mois ou d’année. Il faut donc penser à faire des
ajustements : si je dépasse 60 secondes, je convertis l’excès en minutes ; si je dépasse 60
minutes, je les convertis en heures ; si je dépasse 24 heures, je les convertis en jours. Puis je
vérifie si je dépasse le nombre de jours dans le mois : dans ce cas, je passe au mois suivant, et
si je dépasse 12 mois, je passe à l’année suivante. Pour gérer correctement février, j’ai aussi
inclus une fonction qui vérifie si une année est bissextile.
Cette fonction Dates m’a permis ensuite de calculer les hauteurs d’eau correspondantes, en
utilisant mon prédicteur harmonique pour chaque point du tableau.
1

if ( h [0] > h [1]) {
// Debut en decroissance = > BM1 , PM1 , BM2 , PM2

2
3

} else {
// Debut en croissance = > PM1 , BM1 , PM2 , BM2

4
5

}

Listing 5 – Extrait de la détection des marées

Compte-rendu EDC1

9

Cette macro me permet ainsi d’obtenir les horaires des marées, ainsi qu’un graphe automatique comme ci-dessous :
Type

Date et heure

BM1

10/04/2012 18 :00 :10

PM1

11/04/2012 0 :18 :10

BM2

11/04/2012 6 :30 :10

PM2

11/04/2012 12 :48 :10

Figure 3 – Hauteurs prédites à partir du 10/04/2012 16 :42 :10 (pas de 6 min)
On constate que les horaires obtenus sont parfaitement cohérents avec la forme de la courbe
de hauteur. Le graphe confirme visuellement la présence des deux creux (BM) et des deux
pics (PM) annoncés.
Conclusion : le formulaire fonctionne correctement, il permet de déterminer automatiquement les horaires des marées à partir d’une date donnée, grâce à l’exploitation du modèle
harmonique précédent.

