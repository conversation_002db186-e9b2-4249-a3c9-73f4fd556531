package Simulator;

import fr.emse.fayol.maqit.simulator.configuration.SimProperties;
import fr.emse.fayol.maqit.simulator.components.SituatedComponent;
import fr.emse.fayol.maqit.simulator.environment.ColorCell;
import fr.emse.fayol.maqit.simulator.environment.GridEnvironment;
import fr.emse.fayol.maqit.simulator.display.GraphicalWindow;

/**
 * CLASSE ABSTRAITE SIMFACTORY - FABRIQUE DE BASE POUR LES SIMULATIONS
 *
 * Cette classe abstraite définit la structure de base que toutes les fabriques de simulation
 * doivent suivre. Une fabrique de simulation est responsable de créer et organiser tous
 * les éléments nécessaires pour faire fonctionner une simulation : l'environnement,
 * les robots, les obstacles, les objectifs, etc.
 *
 * Pourquoi une classe abstraite ?
 * - Elle définit les méthodes communes à toutes les simulations (interface graphique, etc.)
 * - Elle force les classes filles à implémenter les méthodes spécifiques (createRobot, etc.)
 * - Elle permet de partager du code commun entre différents types de simulations
 *
 * Les classes qui héritent de SimFactory (comme MySimFactory) doivent implémenter :
 * - createEnvironment() : créer la grille de simulation
 * - createObstacle() : placer les obstacles
 * - createGoal() : définir les zones d'arrivée
 * - createRobot() : créer et placer les robots
 * - schedule() : gérer le déroulement de la simulation
 */
public abstract class SimFactory {

    // Propriétés de configuration de la simulation (taille, couleurs, etc.)
    protected SimProperties proprietes;

    // L'environnement de simulation (la grille où évoluent les robots)
    protected GridEnvironment environnement;

    // Compteur statique pour attribuer des identifiants uniques aux composants
    protected static int compteurIdComposant = 1;

    // La fenêtre graphique qui affiche la simulation
    protected GraphicalWindow fenetreGraphique;

    /**
     * CONSTRUCTEUR DE LA FABRIQUE DE SIMULATION
     *
     * Ce constructeur initialise la fabrique avec les propriétés de configuration.
     * Ces propriétés contiennent toutes les informations nécessaires pour créer
     * la simulation : taille de la grille, nombre de robots, couleurs, etc.
     *
     * @param proprietes Les propriétés de configuration de la simulation
     */
    public SimFactory(SimProperties proprietes) {
        this.proprietes = proprietes;
        this.fenetreGraphique = null;

        // Enregistrer la création de la fabrique dans les logs
        LogManager.getInstance().logAction("SimFactory", "Fabrique de simulation créée");
    }

    /**
     * MÉTHODE POUR INITIALISER L'INTERFACE GRAPHIQUE
     *
     * Cette méthode crée la fenêtre qui affiche la simulation en temps réel.
     * Elle doit être appelée après avoir créé l'environnement et tous ses composants.
     *
     * La fenêtre graphique permet de :
     * - Visualiser l'état de la simulation
     * - Voir les robots se déplacer
     * - Observer les interactions entre composants
     */
    public void initializeGW() {
        // Créer la fenêtre graphique avec l'environnement et les paramètres d'affichage
        fenetreGraphique = new GraphicalWindow(
            (ColorCell[][]) (environnement.getGrid()),
            proprietes.display_x,
            proprietes.display_y,
            proprietes.display_width,
            proprietes.display_height,
            proprietes.display_title
        );
        // Initialiser la fenêtre
        fenetreGraphique.init();

        // Enregistrer l'initialisation dans les logs
        LogManager.getInstance().logAction("SimFactory", "Interface graphique initialisée");
    }

    /**
     * MÉTHODE POUR RAFRAÎCHIR L'INTERFACE GRAPHIQUE
     *
     * Cette méthode met à jour l'affichage de la simulation. Elle doit être
     * appelée à chaque étape de la simulation pour montrer les nouveaux
     * états des robots et de l'environnement.
     */
    public void refreshGW() {
        // Rafraîchir l'affichage de la fenêtre
        fenetreGraphique.refresh();
    }

    /**
     * MÉTHODE ABSTRAITE POUR CRÉER L'ENVIRONNEMENT DE SIMULATION
     *
     * Cette méthode doit être implémentée par chaque classe fille pour créer
     * l'environnement spécifique à son type de simulation. L'environnement
     * est la grille où évoluent tous les composants (robots, obstacles, etc.).
     *
     * Exemple d'implémentation :
     * - Créer une grille de la taille spécifiée
     * - Initialiser toutes les cases
     * - Configurer les paramètres de l'environnement
     */
    public abstract void createEnvironment();

    /**
     * MÉTHODE ABSTRAITE POUR CRÉER LES OBSTACLES
     *
     * Cette méthode doit être implémentée pour placer tous les obstacles
     * dans l'environnement. Les obstacles sont des éléments fixes qui
     * bloquent le passage des robots.
     *
     * Exemple d'implémentation :
     * - Lire les positions des obstacles depuis la configuration
     * - Créer les objets obstacle
     * - Les placer dans l'environnement
     */
    public abstract void createObstacle();

    /**
     * MÉTHODE ABSTRAITE POUR CRÉER LES ROBOTS
     *
     * Cette méthode doit être implémentée pour créer et placer tous les robots
     * dans l'environnement. C'est ici qu'on définit le nombre de robots,
     * leurs types, leurs positions initiales, etc.
     *
     * Exemple d'implémentation :
     * - Créer le nombre de robots spécifié dans la configuration
     * - Leur attribuer des positions libres
     * - Les ajouter à l'environnement
     */
    public abstract void createRobot();

    /**
     * MÉTHODE ABSTRAITE POUR CRÉER LES OBJECTIFS (ZONES D'ARRIVÉE)
     *
     * Cette méthode doit être implémentée pour définir les zones d'arrivée
     * où les robots doivent livrer leurs colis. Ces zones sont les destinations
     * finales des tâches de livraison.
     *
     * Exemple d'implémentation :
     * - Définir les positions des zones d'arrivée
     * - Créer les objets goal avec leurs identifiants
     * - Les associer aux bonnes cases de la grille
     */
    public abstract void createGoal();

    /**
     * MÉTHODE POUR AJOUTER UN NOUVEAU COMPOSANT À L'ENVIRONNEMENT
     *
     * Cette méthode permet d'ajouter n'importe quel composant (robot, obstacle,
     * zone, etc.) à l'environnement de simulation. Elle est utilisée pendant
     * la phase de création des composants.
     *
     * Elle vérifie aussi si le composant est statique et l'enregistre
     * auprès du gestionnaire de composants statiques si nécessaire.
     *
     * @param composant Le composant à ajouter à l'environnement
     */
    public void addNewComponent(SituatedComponent composant) {
        // Récupérer la position du composant
        int[] position = composant.getLocation();
        // Placer le composant dans la grille à sa position
        environnement.setCell(position[0], position[1], composant);

        // Vérifier si c'est un composant statique et l'enregistrer si nécessaire
        if (composant instanceof StaticTransitZone ||
            composant instanceof StaticStartZone ||
            composant instanceof StaticExitZone ||
            composant instanceof StaticObstacle) {
            // Enregistrer le composant statique auprès du gestionnaire
            ((StaticComponentManager) StaticComponentManager.getInstance()).registerStaticComponent(composant);
        }

        // Enregistrer l'ajout dans les logs
        LogManager.getInstance().logAction("SimFactory",
            "Composant ajouté à la position [" + position[0] + "," + position[1] + "]");
    }

    /**
     * MÉTHODE POUR DÉPLACER UN COMPOSANT DANS L'ENVIRONNEMENT
     *
     * Cette méthode permet de déplacer un composant d'une case à une autre.
     * Elle vérifie d'abord si le composant est statique avant d'autoriser
     * le déplacement. Les composants statiques (obstacles, zones) ne peuvent
     * jamais être déplacés.
     *
     * @param positionDepart Position de départ [x, y]
     * @param positionArrivee Position d'arrivée [x, y]
     */
    public void updateEnvironment(int[] positionDepart, int[] positionArrivee) {
        // Vérifier si le composant peut être déplacé (n'est pas statique)
        if (((StaticComponentManager) StaticComponentManager.getInstance()).canMove(positionDepart, positionArrivee)) {
            // Le composant peut être déplacé, effectuer le mouvement
            environnement.moveComponent(positionDepart, positionArrivee);
        } else {
            // Le composant est statique, empêcher le déplacement et enregistrer l'erreur
            LogManager.getInstance().logError("SimFactory",
                "Tentative de déplacement d'un composant statique empêchée: " +
                "de [" + positionDepart[0] + "," + positionDepart[1] + "] à [" + positionArrivee[0] + "," + positionArrivee[1] + "]");
        }
    }

    /**
     * MÉTHODE ABSTRAITE POUR GÉRER LE DÉROULEMENT DE LA SIMULATION
     *
     * Cette méthode doit être implémentée pour définir comment la simulation
     * se déroule dans le temps. C'est le "moteur" de la simulation qui fait
     * avancer tous les composants étape par étape.
     *
     * Exemple d'implémentation :
     * - Boucle principale sur le nombre d'étapes
     * - À chaque étape, faire bouger tous les robots
     * - Gérer les interactions entre composants
     * - Mettre à jour l'affichage
     * - Vérifier les conditions d'arrêt
     */
    public abstract void schedule();
}
