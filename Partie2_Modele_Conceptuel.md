# 2. Modèle Conceptuel

## 2.1 Agent

### Perception
Les robots-agents dans notre système perçoivent leur environnement de manière locale et limitée, ce qui garantit la scalabilité du système :

- **Perception des tâches** : Chaque robot ne perçoit que les tâches disponibles dans sa zone de perception immédiate, évitant ainsi la surcharge d'information qui pourrait survenir avec un grand nombre de tâches dans l'entrepôt
- **Perception de l'environnement physique** : Les robots détectent les obstacles, les autres robots, les stations de charge et les zones de transit dans leur voisinage direct
- **Perception des communications** : Réception des messages d'enchères et des annonces de tâches provenant des autres robots dans leur portée de communication

### Connaissance
Les agents possèdent plusieurs types de connaissances qui leur permettent de prendre des décisions autonomes :

- **Connaissance de la topologie** : Chaque robot connaît la structure de l'entrepôt (grille, positions des obstacles, emplacements des stations de charge)
- **Connaissance de son état interne** : Ni<PERSON><PERSON> de batterie, position actuelle, tâche en cours, capacité de charge
- **Connaissance des protocoles** : Règles d'enchères, formats de messages, procédures de coordination
- **Historique local** : Expérience passée des enchères (succès/échecs) pour améliorer les stratégies de bid

### Processus de Décision
Le processus de décision de chaque agent suit une fonction objectif claire visant à optimiser l'efficacité globale :

- **Fonction objectif principale** : Minimiser le temps total d'exécution des tâches tout en maximisant l'utilisation de la batterie
- **Évaluation des tâches** : Chaque robot calcule un score pour les tâches disponibles basé sur :
  - Distance à la tâche
  - Niveau de batterie restant
  - Priorité de la tâche
  - Charge de travail actuelle
- **Stratégie d'enchères** : Les agents ajustent leurs bids en fonction de leur confiance dans leur capacité à exécuter efficacement la tâche
- **Gestion des ressources** : Décisions autonomes sur quand aller se recharger, quelles tâches accepter ou refuser

### Modèle d'Action
Les agents disposent d'un ensemble d'actions primitives qui peuvent être organisées en automate à états finis :

**Actions de base** :
- Se déplacer vers une position
- Ramasser un objet
- Déposer un objet
- Se recharger à une station
- Envoyer/recevoir des messages

**États principaux** :
- **IDLE** : En attente de nouvelles tâches
- **BIDDING** : Participation à une enchère
- **MOVING_TO_PICKUP** : Déplacement vers le point de ramassage
- **PICKING_UP** : Ramassage de l'objet
- **MOVING_TO_DELIVERY** : Transport vers la destination
- **DELIVERING** : Livraison de l'objet
- **CHARGING** : Recharge de la batterie

## 2.2 Environnement

L'environnement de simulation constitue le cadre physique et logique dans lequel évoluent nos agents robotiques.

### Composition de l'environnement
L'environnement est structuré comme une grille bidimensionnelle qui représente l'entrepôt :

- **Grille spatiale** : Espace discret où chaque cellule peut contenir des robots, des objets, ou des infrastructures
- **Zones fonctionnelles** : 
  - Zones de stockage où les objets sont initialement placés
  - Zones de livraison où les objets doivent être transportés
  - Zones de transit (ColorTransitZone) qui facilitent la navigation
  - Stations de charge pour la maintenance des robots
- **Obstacles statiques** : Éléments immobiles qui contraignent les déplacements
- **Objets dynamiques** : Éléments à transporter qui changent de position

### Mécanisme de perception
La perception dans l'environnement est conçue pour être réaliste et limiter la charge informationnelle :

- **Portée de perception limitée** : Chaque robot ne perçoit que dans un rayon défini autour de sa position
- **Contenu perceptuel** : 
  - Positions des autres robots
  - Objets disponibles pour le ramassage
  - Obstacles et infrastructures
  - Tâches annoncées par d'autres robots
- **Mise à jour dynamique** : La perception est actualisée à chaque cycle de simulation

### Actions sur l'environnement
Les agents peuvent modifier l'état de l'environnement par leurs actions :

- **Modification de position** : Déplacement des robots sur la grille
- **Manipulation d'objets** : Ramassage et dépôt modifiant la localisation des objets
- **Communication** : Diffusion de messages qui influencent les décisions des autres agents
- **Consommation de ressources** : Utilisation de l'énergie et occupation des stations de charge

## 2.3 Interaction

### Interactions physiques
Les agents interagissent physiquement dans l'environnement partagé :

- **Évitement de collisions** : Les robots doivent coordonner leurs mouvements pour éviter les conflits spatiaux
- **Concurrence pour les ressources** : 
  - Compétition pour l'accès aux stations de charge
  - Concurrence pour les objets à ramasser
  - Partage des voies de circulation
- **Effets de congestion** : La densité de robots dans une zone peut ralentir les déplacements et affecter l'efficacité globale

### Protocole d'enchères pour l'allocation des tâches
Le cœur des interactions entre agents repose sur un mécanisme d'enchères décentralisé :

**Phase d'annonce** :
- Un robot découvre une nouvelle tâche
- Il diffuse une annonce contenant les détails de la tâche (position de ramassage, destination, priorité)
- L'annonce se propage dans le réseau de communication

**Phase d'enchères** :
- Les robots intéressés calculent leur capacité à exécuter la tâche
- Chaque robot formule un bid basé sur son score d'évaluation
- Les bids sont transmis au robot initiateur de l'enchère

**Phase d'attribution** :
- Le robot initiateur compare tous les bids reçus
- Il sélectionne le meilleur bid selon les critères définis
- Il notifie le gagnant et informe les autres participants du résultat

### Communication inter-agents
La communication suit un modèle de diffusion locale :

- **Portée limitée** : Les messages ne se propagent que dans un rayon défini
- **Propagation de proche en proche** : Les messages peuvent être relayés par les robots intermédiaires
- **Types de messages** :
  - Annonces de tâches
  - Bids d'enchères
  - Confirmations d'attribution
  - Informations de statut

## 2.4 Organisation

### Règles du système
Le système fonctionne selon des règles organisationnelles claires qui garantissent la cohérence et l'efficacité :

**Règles d'autonomie** :
- Chaque robot prend ses décisions de manière indépendante
- Aucun agent central ne contrôle les actions des robots
- Les robots sont responsables de leur propre gestion énergétique

**Règles de coordination** :
- Les enchères suivent un protocole standardisé
- Les attributions de tâches sont respectées une fois confirmées
- Les robots doivent signaler l'achèvement de leurs tâches

**Règles de priorité** :
- Les tâches urgentes ont la priorité sur les tâches normales
- Les robots en situation critique (batterie faible) ont priorité pour l'accès aux stations de charge
- En cas d'égalité dans les enchères, des critères de départage sont appliqués

### Relations organisationnelles
Les agents entretiennent des relations horizontales sans hiérarchie :

**Égalité des agents** :
- Tous les robots ont les mêmes droits et capacités de base
- Aucun robot n'a d'autorité sur les autres
- Les décisions collectives émergent des interactions individuelles

**Coopération compétitive** :
- Les robots coopèrent pour l'efficacité globale du système
- Ils sont en compétition pour l'attribution des tâches
- Cette tension crée un équilibre dynamique optimisant les performances

### Conséquences organisationnelles

**Avantages** :
- **Robustesse** : La panne d'un robot n'affecte pas le fonctionnement global
- **Scalabilité** : Le système peut facilement s'adapter à un nombre variable de robots
- **Adaptabilité** : Le système s'ajuste automatiquement aux changements de charge de travail
- **Efficacité** : La compétition entre robots optimise naturellement l'allocation des ressources

**Défis** :
- **Coordination complexe** : Sans contrôle central, certaines situations peuvent créer des inefficacités locales
- **Équité** : Risque que certains robots soient systématiquement favorisés dans les enchères
- **Convergence** : Le système doit garantir que toutes les tâches sont finalement exécutées
- **Communication** : La dépendance aux communications peut créer des points de défaillance

**Mécanismes de régulation** :
- Timeouts pour éviter les blocages dans les enchères
- Mécanismes de fallback en cas d'échec de communication
- Algorithmes d'équilibrage pour répartir équitablement la charge de travail
- Monitoring distribué pour détecter les dysfonctionnements
