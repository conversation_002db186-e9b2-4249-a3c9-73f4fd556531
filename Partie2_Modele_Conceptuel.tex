\section{Modèle Conceptuel}

Le modèle conceptuel de notre système d'entrepôt automatisé s'articule autour de quatre composantes fondamentales qui s'imbriquent de manière logique. Cette présentation suit une progression du particulier vers le général : nous commençons par définir l'agent robotique individuel, puis nous décrivons l'environnement dans lequel il évolue, ensuite nous analysons les interactions entre agents multiples, et enfin nous examinons l'organisation globale du système.

\subsection{Agent}

Dans notre système, chaque robot est un agent autonome. Il peut percevoir ce qui l'entoure, stocker des infos, prendre des décisions, et agir en fonction. Pour bien comprendre le comportement de chaque robot, on peut séparer ça en quatre parties : perception, connaissance, prise de décision et action.

\subsubsection{Perception}
Chaque robot a une vision locale et limitée de ce qu’il y a autour. Ce choix a deux avantages : ça évite de devoir tout percevoir (ce qui serait lourd), et ça reste plus réaliste.

Pour les \textbf{tâches}, le robot ne les voit que si elles sont proches de lui. Comme ça, il ne s’encombre pas avec des informations qui seraient trop loin et qui ne le concernent pas.

Il détecte aussi ce qu’il y a physiquement autour : les autres robots, les obstacles, les stations de recharge, et les zones de transit colorées.

Enfin, il peut aussi \textbf{écouter} les messages envoyés par les autres robots dans un certain rayon. C’est ce qui lui permet de rester coordonné dans un système distribué.

\subsubsection{Connaissance}
Pour fonctionner, chaque robot a besoin d’un minimum de connaissances en plus de ce qu’il perçoit sur le moment.

D’abord, il connaît la \textbf{carte de l’entrepôt} : où sont les obstacles, les zones spéciales, la forme de la grille, etc.


Il garde aussi en mémoire son propre \textbf{état} : sa batterie, où il est, s’il porte un colis ou pas, etc.

Ensuite, il a en mémoire les \textbf{règles de communication} : comment on fait une enchère, à quoi ressemble un message, comment on coordonne les tâches.

Enfin, il garde un \textbf{historique} de ce qu’il a déjà fait : s’il a gagné ou perdu des enchères, comment ça s’est passé, etc. Cela lui permet d’apprendre de ses erreurs ou réussites.

\subsubsection{Processus de Décision (Système d'enchères)}
C’est cette partie qui relie tout : les informations qu’il perçoit, ce qu’il connaît, et ce qu’il doit faire. L’objectif est simple : faire les tâches le plus vite possible, sans tomber en panne de batterie.

Quand une tâche est disponible, il la note avec un score. Ce score dépend de plusieurs choses : la distance, la batterie, l’importance de la tâche, et ce qu’il est déjà en train de faire.

Ensuite, il décide s’il veut faire une offre pour cette tâche. Plus il est proche et en forme, plus il proposera une bonne offre.

Enfin, il gère ses priorités tout seul : il décide quand il a besoin de se recharger ou quand il vaut mieux refuser une tâche.

\subsubsection{Modèle d'Action}
Une fois qu’il a pris une décision, le robot passe à l’action. Tout ça est géré par un système d'états. Chaque état correspond à une action, et le robot change d’état selon ce qu’il se passe.

Voici les \textbf{actions} qu’un robot peut faire :
\begin{itemize}
    \item se déplacer d’une case
    \item ramasser un colis
    \item déposer un colis
    \item se recharger
    \item envoyer ou recevoir un message
\end{itemize}

\paragraph{Les 5 états principaux du robot :}

Et voici les \textbf{états} qu’il peut avoir :
\begin{itemize}
    \item \texttt{FREE} : Robot libre et disponible pour récupérer des colis
    \item \texttt{GOING\_TO\_START} : Robot se dirige vers une zone de départ pour prendre un colis
    \item \texttt{GOING\_TO\_TRANSIT} : Robot se dirige vers une zone de transit pour déposer un colis
    \item \texttt{GOING\_TO\_GOAL} : Robot se dirige vers la destination finale pour livrer
    \item \texttt{WAITING\_AT\_TRANSIT} : Robot attend à une zone de transit (état d'attente)
    \item \texttt{PICKING\_FROM\_TRANSIT} : Robot récupère un colis depuis une zone de transit
    \item \texttt{DELIVERED} : Robot a livré le colis avec succès
    \item \texttt{RETURNING\_TO\_CENTER} : Robot retourne vers la zone d'attente centrale
\end{itemize}
\vspace{0.5em}

\noindent\includegraphics[width=\linewidth]{a.PNG}

\subsection{Notre environnement de simulation : Un entrepôt virtuel réaliste}

Notre système reproduit fidèlement la complexité d'un environnement de simulation sophistiqué organisé sur une grille de 50×50 cases (soit 2500 emplacements possibles). Voici les composants essentiels :

\paragraph{Les zones d'entrée (StartZones) - Les quais de réception}
\begin{itemize}
    \item \textbf{Fonction} : Points d'apparition des nouveaux colis à traiter
    \item \textbf{Analogie} : Équivalents aux quais de réception d'un vrai entrepôt où arrivent les camions
    \item \textbf{Caractéristiques} : Capacité limitée (3-5 colis maximum), génération aléatoire de colis avec destinations variées
    \item \textbf{Positions dans notre simulation} : [5,5], [15,5], [25,5], [35,5], [45,5] (réparties sur le bord nord)
    \item \textbf{Comportement} : Quand un colis apparaît, la zone devient "visible" aux robots qui patrouillent
\end{itemize}

\paragraph{Les zones de transit colorées (ColorTransitZones) - L'innovation majeure}
\begin{itemize}
    \item \textbf{Fonction} : Points de relais stratégiques pour optimiser les flux
    \item \textbf{Analogie} : Comme des "parkings intermédiaires" ou des "hubs de distribution" dans un réseau logistique
    \item \textbf{Innovation} : Contrairement à un trajet direct, un robot peut déposer temporairement son colis dans une zone de transit, permettant à un autre robot mieux placé de terminer la livraison
    \item \textbf{Avantage} : Découplage de la collecte et de la livraison, réduction des embouteillages
    \item \textbf{Positions} : [15,15], [25,25], [35,35] (triangle central pour couverture optimale)
    \item \textbf{Couleurs} : Chaque zone a une couleur spécifique (rouge, bleu, verte) pour identification visuelle
\end{itemize}

\paragraph{Les zones de sortie (ExitZones) - Les destinations finales}
\begin{itemize}
    \item \textbf{Fonction} : Destinations finales où les colis doivent être livrés
    \item \textbf{Analogie} : Quais d'expédition, zones de stockage spécialisées, ou points de collecte clients
    \item \textbf{Positions} : [10,45], [20,45], [30,45], [40,45] (réparties sur le bord sud)
    \item \textbf{Validation} : Chaque livraison est automatiquement vérifiée et comptabilisée
\end{itemize}

\paragraph{Les stations de charge - L'infrastructure énergétique}
\begin{itemize}
    \item \textbf{Fonction} : Points de recharge pour maintenir l'autonomie des robots
    \item \textbf{Nécessité} : Chaque mouvement consomme 1\% de batterie, chaque prise/dépôt 2\%
    \item \textbf{Nombre} : 12 stations réparties stratégiquement sur la grille 50×50
    \item \textbf{Stratégie} : Positionnement calculé pour éviter les congestions et garantir l'accessibilité
    \item \textbf{Vitesse de charge} : 5\% par étape de simulation
\end{itemize}

\subsection{Interaction}

Maintenant qu’on a vu comment fonctionne un robot tout seul, on peut s’intéresser à ce qui se passe quand plusieurs robots partagent le même environnement. Dans ce cas, ils doivent interagir. Ces interactions peuvent être physiques (quand ils se gênent ou se croisent) ou via des messages (quand ils se parlent pour s’organiser).

\subsubsection{Interactions physiques}
Quand plusieurs robots sont présents au même endroit, ils doivent faire attention à ne pas se gêner.

L’\textbf{évitement de collisions} est un problème constant. Comme ils sont plusieurs à se déplacer sur la même grille, il faut qu’ils trouvent comment éviter de se rentrer dedans. Chacun adapte son chemin en fonction des autres.

Il y a aussi de la \textbf{compétition pour les ressources} :
\begin{itemize}
    \item Parfois, plusieurs robots veulent se recharger au même endroit, donc ça crée des files d’attente.
    \item Pour ramasser un objet, c’est le plus rapide qui gagne.
    \item Les chemins sont partagés, donc parfois il faut attendre ou passer ailleurs si un robot bloque le passage.
    \item Aussi, lorsqu’un robot dépose un colis, il ne doit pas rester dans la zone de dépôt, car il empêcherait les autres robots d’y accéder pour déposer à leur tour.
    \item ...
\end{itemize}

Enfin, plus il y a de robots au même endroit, plus il y a de \textbf{congestion}. Quand c’est trop chargé, ça ralentit tout le monde.

\subsubsection{Protocole d'enchères pour l'allocation des tâches}
Pour se répartir les tâches, les robots utilisent un système d’enchères en trois étapes. C’est décentralisé : aucun robot ne décide pour les autres.

\textbf{Annonce} :
\begin{itemize}
    \item Dès qu’un robot repère une tâche, il envoie une annonce.
    \item Cette tâche contient tout : où est l’objet, où il faut le livrer, et si c’est urgent.
    \item Le message (contenant toutes les informations) se diffuse aux autres robots autour leur permettant d'avoir également toutes les informations sur la tâche grâce au système de communication.
\end{itemize}

\textbf{Enchères} :
\begin{itemize}
    \item Chaque robot qui reçoit l’annonce regarde s’il peut faire la tâche.
    \item Il calcule un score en fonction de la distance, de sa batterie, et de ce qu’il est déjà en train de faire.
    \item S’il pense être un bon candidat, il envoie une offre.
\end{itemize}

\textbf{Attribution} :
\begin{itemize}
    \item L’initiateur de l’annonce compare les offres.
    \item Il choisit la meilleure, attribue la tâche, et prévient tout le monde.
\end{itemize}

\subsubsection{Communication inter-agents}
Les robots communiquent entre eux grâce à un système de messages, mais avec une portée limitée.

Chaque robot peut parler seulement à ceux qui sont proches (\textbf{portée locale}). Ça évite que tout le monde reçoive tout, et ça reste plus proche de la réalité.

Grâce à la \textbf{propagation en plusieurs sauts}, un message peut quand même aller loin : chaque robot peut le retransmettre, un peu comme un relais.

Les types de messages utilisés sont simples :
\begin{itemize}
    \item Des annonces de nouvelles tâches
    \item Des offres pour ces tâches
    \item Des confirmations pour dire qui a gagné
    \item Des infos sur l’état ou la disponibilité d’un robot
\end{itemize}

\subsection{Organisation}

Une fois qu’on a vu comment les robots interagissent entre eux, on peut s’intéresser à comment tout ça donne une organisation globale. En gros, c’est le comportement collectif qui ressort de la somme des décisions individuelles et des échanges locaux.

\subsubsection{Règles du système}

Le fonctionnement du système repose sur un ensemble de règles qui assurent que tout reste cohérent et efficace, même sans chef.

Les \textbf{règles d’autonomie} sont les bases du système décentralisé :
\begin{itemize}
    \item Chaque robot prend ses décisions tout seul
    \item Il n’y a pas de chef ou de hiérarchie
    \item Chacun gère sa batterie de son côté
\end{itemize}

Les \textbf{règles de coordination} permettent que tout fonctionne bien ensemble :
\begin{itemize}
    \item Tous les robots suivent exactement le protocole d’enchères
    \item Quand un robot accepte une tâche, il doit la faire
    \item À la fin, il doit annoncer qu’il a fini
\end{itemize}

Les \textbf{règles de priorité} aident à gérer les conflits :
\begin{itemize}
    \item Les tâches urgentes passent avant les autres
    \item Les robots qui ont presque plus de batterie sont prioritaires pour se recharger
    \item S’il y a égalité dans une enchère, des critères prévus tranchent
\end{itemize}

\subsubsection{Relations organisationnelles}

L’organisation est totalement horizontale. Aucun robot n’a plus de pouvoir qu’un autre.

Tous les robots sont sur un \textbf{pied d’égalité} :
\begin{itemize}
    \item Ils ont tous les mêmes droits et les mêmes capacités de base
    \item Il n’y a pas d’ordre donné ou de rôle supérieur (système décentralisé)
    \item Les décisions collectives apparaissent naturellement, sans chef
\end{itemize}

On parle aussi de \textbf{coopération compétitive} :
\begin{itemize}
    \item Ils coopèrent pour que le système soit efficace
    \item Mais ils sont aussi en compétition pour prendre les tâches
    \item Cet équilibre permet d’atteindre de bonnes performances globales
\end{itemize}

\subsubsection{Conséquences organisationnelles}

Cette organisation apporte plusieurs \textbf{avantages} :
\begin{itemize}
    \item \textbf{Robustesse} : Si un robot tombe en panne, les autres continuent
    \item \textbf{Scalabilité} : On peut ajouter ou enlever des robots sans tout changer
    \item \textbf{Adaptabilité} : Le système s’adapte si la charge de travail change
    \item \textbf{Efficacité} : La compétition pousse à faire mieux
\end{itemize}

Mais elle apporte aussi des \textbf{difficultés} :
\begin{itemize}
    \item \textbf{Coordination} : Pas toujours facile de bien se synchroniser sans chef
    \item \textbf{Équité} : Risque que certains prennent toujours les meilleures tâches
    \item \textbf{Convergence} : Il faut s’assurer que toutes les tâches soient faites
    \item \textbf{Communication} : Si les messages ne passent pas, tout peut être bloqué
\end{itemize}