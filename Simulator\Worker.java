package Simulator;

import java.awt.Color;
import java.util.Random;

import fr.emse.fayol.maqit.simulator.components.ColorInteractionRobot;
import fr.emse.fayol.maqit.simulator.components.Message;
import fr.emse.fayol.maqit.simulator.components.Orientation;

/**
 * CLASSE WORKER - ROBOT TRAVAILLEUR AVEC MOUVEMENT ALÉATOIRE
 *
 * Cette classe représente un robot travailleur qui se déplace de manière aléatoire
 * dans l'entrepôt. Contrairement aux robots de livraison, les workers n'ont pas
 * d'objectif précis : ils bougent simplement pour simuler l'activité dans l'entrepôt.
 *
 * Comportement du worker :
 * - Se déplace dans une direction pendant un certain nombre d'étapes
 * - Change de direction périodiquement ou quand il rencontre un obstacle
 * - Utilise le système de journalisation pour tracer ses mouvements
 * - Ne traite pas les messages des autres robots (il est indépendant)
 *
 * Ce type de robot peut représenter des employés qui se déplacent dans l'entrepôt
 * pour diverses tâches de maintenance ou de supervision. Il agit comme un "obstacle mobile"
 * que les autres robots doivent contourner.
 */
public class Worker extends ColorInteractionRobot {

    // Générateur de nombres aléatoires pour choisir les directions
    private Random generateurAleatoire;

    // Compteur qui suit combien de mouvements le worker a fait dans la direction actuelle
    private int compteurMouvements = 0;

    // Constante qui définit après combien de mouvements le worker change de direction
    // Plus cette valeur est grande, plus le worker va loin dans une direction
    private static final int INTERVALLE_CHANGEMENT_DIRECTION = 10;

    /**
     * CONSTRUCTEUR DU WORKER
     *
     * Ce constructeur crée un nouveau robot travailleur avec les paramètres donnés.
     * Il appelle le constructeur de la classe parente (ColorInteractionRobot) pour
     * initialiser les propriétés de base du robot, puis configure ses propres
     * caractéristiques spécifiques.
     *
     * @param nom Le nom du worker (ex: "Worker0", "Worker1", etc.)
     * @param champ Le champ de vision du robot (rayon en cases)
     * @param debug Mode debug (0 = désactivé, 1 = activé)
     * @param position Position initiale du robot [x, y]
     * @param couleur Couleur d'affichage du robot
     * @param lignes Nombre de lignes de la grille
     * @param colonnes Nombre de colonnes de la grille
     * @param graine Graine pour le générateur aléatoire
     */
    public Worker(String nom, int champ, int debug, int[] position, Color couleur, int lignes, int colonnes, long graine) {
        // Appeler le constructeur de la classe parente avec tous les paramètres
        super(nom, champ, debug, position, couleur, lignes, colonnes, graine);

        // Définir l'orientation initiale vers le haut
        orientation = Orientation.up;

        // Créer un générateur aléatoire unique pour ce worker
        // On utilise la graine + le hash du nom pour que chaque worker ait un comportement différent
        generateurAleatoire = new Random(graine + nom.hashCode());

        // Enregistrer la création du worker dans les logs
        LogManager.getInstance().logAction(nom, "Worker créé et prêt à se déplacer aléatoirement");
    }

    /**
     * MÉTHODE DE DÉPLACEMENT APPELÉE PAR LE SIMULATEUR
     *
     * Cette méthode est appelée automatiquement par le simulateur à chaque étape.
     * Elle fait simplement appel à la méthode step() qui contient la logique
     * de déplacement du worker. Cette séparation permet de garder le code organisé.
     *
     * @param etape Le numéro de l'étape actuelle (non utilisé ici)
     */
    @Override
    public void move(int etape) {
        // Déléguer le comportement à la méthode step()
        step();
    }

    /**
     * MÉTHODE PRINCIPALE POUR LE COMPORTEMENT DU WORKER
     *
     * Cette méthode contient toute la logique de déplacement du worker.
     * Elle suit cet algorithme simple :
     *
     * 1. Incrémenter le compteur de mouvements
     * 2. Vérifier s'il faut changer de direction (soit par intervalle, soit par obstacle)
     * 3. Essayer de se déplacer dans la direction actuelle
     * 4. Si bloqué, changer de direction et réessayer
     * 5. Enregistrer l'action dans les logs
     *
     * Cette approche simple mais efficace crée un mouvement aléatoire réaliste.
     */
    public void step() {
        // Étape 1 : Incrémenter le compteur de mouvements dans la direction actuelle
        compteurMouvements++;

        // Étape 2 : Vérifier s'il faut changer de direction
        // Deux conditions peuvent déclencher un changement :
        // - Le worker a fait assez de mouvements dans cette direction
        // - Il y a un obstacle devant lui
        if (compteurMouvements >= INTERVALLE_CHANGEMENT_DIRECTION || !freeForward()) {
            // Choisir une nouvelle direction aléatoire
            randomOrientation();
            // Remettre le compteur à zéro pour cette nouvelle direction
            compteurMouvements = 0;

            // Enregistrer le changement de direction dans les logs
            LogManager.getInstance().logAction(getName(), "change de direction");
        }

        // Étape 3 : Essayer de se déplacer dans la direction actuelle
        if (freeForward()) {
            // Le chemin est libre, on peut avancer
            moveForward();
            // Enregistrer le mouvement réussi
            LogManager.getInstance().logAction(getName(), "se déplace vers l'avant");
        } else {
            // Étape 4 : Le chemin est bloqué, essayer une autre direction
            randomOrientation();
            // Remettre le compteur à zéro car on a changé de direction
            compteurMouvements = 0;

            // Essayer de bouger dans cette nouvelle direction
            if (freeForward()) {
                moveForward();
                LogManager.getInstance().logAction(getName(), "contourne un obstacle et se déplace");
            } else {
                // Même la nouvelle direction est bloquée, rester sur place
                LogManager.getInstance().logAction(getName(), "est bloqué et reste sur place");
            }
        }
    }

    /**
     * MÉTHODE POUR TRAITER LES MESSAGES REÇUS
     *
     * Cette méthode est appelée quand le worker reçoit un message d'un autre robot.
     * Pour l'instant, les workers ne traitent aucun message car ils sont indépendants
     * et n'ont pas besoin de communiquer avec les autres robots.
     *
     * Dans une version plus avancée, on pourrait imaginer que les workers :
     * - Répondent aux demandes d'information sur leur position
     * - Reçoivent des instructions pour éviter certaines zones
     * - Communiquent entre eux pour coordonner leurs mouvements
     *
     * @param message Le message reçu d'un autre robot
     */
    @Override
    public void handleMessage(Message message) {
        // Pour l'instant, les workers ignorent tous les messages
        // On pourrait ajouter ici du code pour traiter certains types de messages
        LogManager.getInstance().logAction(getName(), "a reçu un message mais l'ignore");
    }
}
