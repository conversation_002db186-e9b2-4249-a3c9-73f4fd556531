package Simulator;

import fr.emse.fayol.maqit.simulator.configuration.IniFile;
import fr.emse.fayol.maqit.simulator.configuration.SimProperties;
import java.awt.Color;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import fr.emse.fayol.maqit.simulator.components.ColorPackage;
import fr.emse.fayol.maqit.simulator.components.Robot;
import fr.emse.fayol.maqit.simulator.environment.Cell;
import fr.emse.fayol.maqit.simulator.environment.ColorCell;
import fr.emse.fayol.maqit.simulator.environment.ColorGoal;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;

// Classe principale qui permet de gérer toute la simulation : création des composants, gestion du temps, etc.
public class MySimFactory extends SimFactory {

    // On utilise une map pour retrouver une zone de départ grâce à son identifiant (ex : "A1")
    private Map<String, StaticStartZone> startZonesMap = new HashMap<>();

    // Variable statique qui compte le nombre total de colis livrés
    public static int deliveredCount = 0;

    // Variables liées à la simulation (nombre de colis total, non générés, nb employés...)
    int nbPackages;
    int nbNotGeneratedPackets;
    int numberOfWorkers;
    Random rnd; // Générateur aléatoire
    int totalSteps = 0; // Nombre total d'étapes de la simulation

    // Coordonnées fixes pour les deux zones d'arrivée (Z1 et Z2)
    protected static final Map<Integer, int[]> GOALS = new HashMap<>();
    static {
        GOALS.put(1, new int[]{5, 0});
        GOALS.put(2, new int[]{15, 0});
    }

    // Constructeur de la classe, on appelle juste le constructeur de la classe parente avec les propriétés
    public MySimFactory(SimProperties sp) {
        super(sp);
    }

    // Fonction qui crée l'environnement de simulation, cad la grille dans laquelle tout se passe
    @Override
    public void createEnvironment() {
        environnement = new ColorGridEnvironment(proprietes.rows, proprietes.columns, proprietes.debug, proprietes.seed); // on crée la grille
        environnement.initializeGrid(); // on initialise toutes les cases de la grille
        ((StaticComponentManager) StaticComponentManager.getInstance()).setEnvironment((ColorGridEnvironment) environnement); // on lie la grille au gestionnaire
        LogManager.getInstance().logAction("MySimFactory", "Gestionnaire de composants statiques initialisé");
    }

    // Cette méthode crée les obstacles dans la grille. Chaque obstacle est placé à une position définie dans le fichier .ini
    @Override
    public void createObstacle() {
        for (int[] pos : proprietes.obstaclePositions) {
            StaticObstacle obstacle = new StaticObstacle(pos,
                new int[]{proprietes.colorobstacle.getRed(), proprietes.colorobstacle.getGreen(), proprietes.colorobstacle.getBlue()});
            addNewComponent(obstacle); // on ajoute l'obstacle à la grille
        }
    }

    // Fonction qui crée les zones d'arrivée (les objectifs des robots)
    @Override
    public void createGoal() {
        int[] z1Pos = proprietes.goalPositions.get(1);
        int[] z2Pos = proprietes.goalPositions.get(2);

        // Zone Z1
        ((ColorCell) environnement.getGrid()[z1Pos[0]][z1Pos[1]]).setGoal(
            new ColorGoal(1, new int[]{proprietes.colorgoal.getRed(), proprietes.colorgoal.getGreen(), proprietes.colorgoal.getBlue()})
        );

        // Zone Z2
        ((ColorCell) environnement.getGrid()[z2Pos[0]][z2Pos[1]]).setGoal(
            new ColorGoal(2, new int[]{proprietes.colorgoal.getRed(), proprietes.colorgoal.getGreen(), proprietes.colorgoal.getBlue()})
        );
    }

    // Fonction qui permet de créer un certain nombre de colis. Chaque colis est envoyé depuis une zone de départ vers une destination aléatoire
    public void createPackages(int nbpackages) {
        String[] startZones = {"A1", "A2", "A3"}; // Liste des zones de départ possibles

        for (int i = 0; i < nbpackages; i++) {
            int destinationId = rnd.nextInt(2) + 1; // soit 1 soit 2
            int ts = 0; // temps de génération, ici toujours 0
            int randomStartZone = rnd.nextInt(startZones.length); // choix aléatoire d'une zone de départ
            String zone = startZones[randomStartZone];
            int[] position = {-1, -1}; // on met -1 pour ne pas dessiner le colis

            // Création de l'objet colis
            ColorPackage pack = new ColorPackage(position,
                new int[]{proprietes.colorpackage.getRed(), proprietes.colorpackage.getGreen(), proprietes.colorpackage.getBlue()},
                destinationId, ts, zone);

            StaticStartZone startZone = getStartZoneById(zone); // on récupère la zone de départ correspondante
            if (startZone != null) {
                startZone.addPackage(pack); // on ajoute le colis à la zone de départ
                LogManager.getInstance().logAction("Factory", "Création d'un nouveau colis " + pack.getId() + " de " + zone + " vers la destination " + destinationId);
            } else {
                LogManager.getInstance().logError("Factory", "La zone de départ " + zone + " n'existe pas !");
            }
        }
    }

    // Permet de retrouver une zone de départ à partir de son identifiant (utile pour placer les colis)
    public StaticStartZone getStartZoneById(String id) {
        return startZonesMap.get(id);
    }

    // Crée les zones de départ à partir des données contenues dans le fichier .ini
    public void createStartZones() {
        for (Map.Entry<String, int[]> entry : proprietes.startZonePositions.entrySet()) {
            String zoneId = entry.getKey();
            int[] pos = entry.getValue();
            StaticStartZone zone = new StaticStartZone(pos,
                new int[]{proprietes.colorstartzone.getRed(), proprietes.colorstartzone.getGreen(), proprietes.colorstartzone.getBlue()});
            addNewComponent(zone);
            startZonesMap.put(zoneId, zone); // on mémorise cette zone pour y accéder facilement plus tard
        }
    }

    // Crée les zones de transit, c'est-à-dire les zones intermédiaires entre le départ et l'arrivée
    public void createTransitZones() {
        for (int[] data : proprietes.transitZoneData) {
            int x = data[0];
            int y = data[1];
            int capacity = data[2];
            StaticTransitZone tz = new StaticTransitZone(new int[]{x, y},
                new int[]{proprietes.colortransitzone.getRed(), proprietes.colortransitzone.getGreen(), proprietes.colortransitzone.getBlue()}, capacity);
            addNewComponent(tz);
        }
    }

    // Ajoute les zones de sortie, utilisées comme point de fin pour certains composants
    public void createExitZones() {
        for (int[] pos : proprietes.exitZonePositions) {
            StaticExitZone exitZone = new StaticExitZone(pos,
                new int[]{proprietes.colorexit.getRed(), proprietes.colorexit.getGreen(), proprietes.colorexit.getBlue()});
            addNewComponent(exitZone);
        }
    }

    // Ajoute des travailleurs (robots jaunes).
    public void createWorker() {
        for (int i = 0; i < numberOfWorkers; i++) {
            int[] pos = environnement.getPlace(); // on choisit une position libre
            Worker worker = new Worker("Worker" + i, proprietes.field, proprietes.debug, pos,
                new Color(proprietes.colorother.getRed(), proprietes.colorother.getGreen(), proprietes.colorother.getBlue()),
                proprietes.rows, proprietes.columns, proprietes.seed);
            addNewComponent(worker);
        }
    }

    // Création des robots. Ici on utilise la classe MyTransitRobot (car on a un système avec transit)
    @Override
    public void createRobot() {
        for (int i = 0; i < proprietes.nbrobot; i++) {
            int[] pos = environnement.getPlace();
            MyRobot robot = new MyTransitRobot("Robot" + i, proprietes.field, proprietes.debug, pos,
                new Color(proprietes.colorrobot.getRed(), proprietes.colorrobot.getGreen(), proprietes.colorrobot.getBlue()),
                proprietes.rows, proprietes.columns, (ColorGridEnvironment) environnement, proprietes.seed);
            addNewComponent(robot);
        }
    }

    // Cette méthode est le coeur de la simulation : elle s'exécute à chaque étape de temps et fait avancer les robots, génère les colis, etc.
    @Override
    public void schedule() {
        List<Robot> robots = environnement.getRobot(); // récupération de tous les robots

        int currentNBPacket;
        for (int i = 0; i < proprietes.step; i++) { // boucle principale de la simulation
            totalSteps++;

            // Si on n'a pas encore généré tous les colis, on peut en créer à ce tour
            if (nbNotGeneratedPackets > 0 && validGeneration()) {
                if (nbNotGeneratedPackets > 2)
                    currentNBPacket = rnd.nextInt(nbNotGeneratedPackets / 2 + 1);
                else
                    currentNBPacket = 2;

                createPackages(currentNBPacket);
                nbNotGeneratedPackets -= currentNBPacket;
            }

            List<Robot> activeRobots = new ArrayList<>();

            for (Robot r : robots) {
                // Si le robot a déjà livré son colis, on ne le bouge plus
                if (r instanceof MyTransitRobot && ((MyTransitRobot) r).hasDelivered()) continue;
                activeRobots.add(r);

                int[] prevPos = r.getLocation(); // on garde l'ancienne position
                Cell[][] perception = environnement.getNeighbor(r.getX(), r.getY(), r.getField()); // champ de vision
                r.updatePerception(perception); // mise à jour de ce que voit le robot

                // appel de la fonction step selon le type de robot
                if (r instanceof MyTransitRobot) {
                    ((MyTransitRobot) r).step();
                } else if (r instanceof MyRobot) {
                    ((MyRobot) r).step();
                } else if (r instanceof Worker) {
                    ((Worker) r).step();
                }

                updateEnvironment(prevPos, r.getLocation()); // on met à jour la nouvelle position dans la grille
            }

            robots = activeRobots; // on ne garde que les robots actifs
            refreshGW(); // mise à jour de l'interface graphique

            // Si tous les colis sont livrés, on arrête la simulation
            if (MySimFactory.deliveredCount >= nbPackages) {
                LogManager.getInstance().logAction("Simulation", "Tous les paquets sont livrés en " + totalSteps + " étapes.");
                LogManager.getInstance().printStatistics();
                break;
            }

            try {
                Thread.sleep(proprietes.waittime); // pause entre chaque étape (pour l'affichage)
            } catch (InterruptedException e) {
                LogManager.getInstance().logError("Simulation", "Erreur lors de la pause: " + e.getMessage());
            }
        }
    }

    // Permet de savoir si on est autorisé à générer de nouveaux colis (tous les 10 steps)
    private boolean validGeneration() {
        if (totalSteps % 10 == 0) return true;
        return false;
    }

    public static void main(String[] args) throws Exception {
        LogManager logManager = LogManager.getInstance();
        logManager.setVerboseMode(true);

        IniFile ifile = new IniFile("parameters/configuration.ini");
        IniFile ifilenv = new IniFile("parameters/environment.ini");

        SimProperties sp = new SimProperties(ifile);
        sp.simulationParams();
        sp.displayParams();

        SimProperties envProp = new SimProperties(ifilenv);
        envProp.loadObstaclePositions();
        envProp.loadStartZonePositions();
        envProp.loadTransitZones();
        envProp.loadExitZonePositions();
        envProp.loadGoalPositions();

        sp.obstaclePositions = envProp.obstaclePositions;
        sp.startZonePositions = envProp.startZonePositions;
        sp.transitZoneData = envProp.transitZoneData;
        sp.exitZonePositions = envProp.exitZonePositions;
        sp.goalPositions = envProp.goalPositions;

        logManager.logAction("Simulation", "Environnement de taille " + sp.rows + "x" + sp.columns);

        MySimFactory sim = new MySimFactory(sp);
        sim.nbPackages = 10;
        sim.nbNotGeneratedPackets = sim.nbPackages;
        sim.numberOfWorkers = sp.nbobstacle / 2;
        sim.rnd = new Random(sp.seed);

        sim.createEnvironment();
        sim.createObstacle();
        sim.createGoal();
        sim.createStartZones();
        sim.createTransitZones();
        sim.createExitZones();
        sim.createWorker();
        sim.createRobot();

        sim.initializeGW();
        sim.schedule();
    }
}
