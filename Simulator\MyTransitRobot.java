package Simulator;

import fr.emse.fayol.maqit.simulator.components.ColorPackage;
import fr.emse.fayol.maqit.simulator.components.PackageState;
import fr.emse.fayol.maqit.simulator.components.ColorTransitZone;
import fr.emse.fayol.maqit.simulator.components.ColorStartZone;
import fr.emse.fayol.maqit.simulator.components.Robot;
import fr.emse.fayol.maqit.simulator.environment.Cell;
import fr.emse.fayol.maqit.simulator.environment.ColorCell;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;

import java.awt.Color;
import java.util.List;

/**
 * CLASSE POUR LA GESTION DE ROBOTS AVEC ZONES DE TRANSIT
 *
 * Cette classe étend MyRobot pour ajouter la capacité d'utiliser des zones de transit
 * comme points de relais pour optimiser les livraisons. Elle implémente également
 * un système de coordination décentralisé entre robots via des enchères.
 *
 * Fonctionnalités principales :
 * - Gestion intelligente des zones de transit
 * - Optimisation des trajets (direct vs transit)
 * - Communication et coordination entre robots
 * - Gestion avancée de la batterie
 */
public class MyTransitRobot extends MyRobot {

    /**
     * ÉNUMÉRATION POUR SUIVRE L'ÉTAT PRÉCIS DU ROBOT AVEC LES ZONES DE TRANSIT
     *
     * Cette énumération définit tous les états possibles d'un robot de transit
     * dans le système décentralisé. Chaque état correspond à une phase spécifique
     * du cycle de vie d'une mission de livraison avec zones de transit.
     *
     * Les états permettent au robot de :
     * - Savoir exactement où il en est dans sa mission
     * - Prendre les bonnes décisions selon son état actuel
     * - Communiquer son statut aux autres robots
     * - Optimiser ses déplacements selon sa situation
     */
    public enum TransitState {
        FREE,               // Robot libre et disponible pour récupérer des colis
        GOING_TO_START,    // Robot se dirige vers une zone de départ pour prendre un colis
        GOING_TO_TRANSIT,  // Robot se dirige vers une zone de transit pour déposer un colis
        GOING_TO_GOAL,     // Robot se dirige vers la destination finale pour livrer
        WAITING_AT_TRANSIT,// Robot attend à une zone de transit (état d'attente)
        PICKING_FROM_TRANSIT, // Robot récupère un colis depuis une zone de transit
        DELIVERED,         // Robot a livré le colis avec succès
        RETURNING_TO_CENTER // Robot retourne vers la zone d'attente centrale
    }

    // VARIABLES D'ÉTAT DU ROBOT DE TRANSIT
    // Ces variables gardent trace de l'état actuel et de la position cible du robot
    private TransitState transitState;      // État actuel du robot dans son cycle de mission
    private int transitX;                   // Coordonnée X de la zone de transit ciblée
    private int transitY;                   // Coordonnée Y de la zone de transit ciblée

    // GESTION DE LA BATTERIE DU ROBOT
    // Cette variable suit le niveau d'énergie actuel du robot (0-100%)
    private double batteryLevel = 100.0;    // Niveau de batterie initial à 100%

    // CONSTANTES POUR LA COMMUNICATION ENTRE ROBOTS
    // Ces chaînes définissent les types de messages que les robots s'envoient
    private static final String MSG_HELP_REQUEST = "HELP_REQUEST";         // Demande d'aide d'un robot
    private static final String MSG_HELP_OFFER = "HELP_OFFER";             // Offre d'aide d'un robot
    private static final String MSG_TRANSIT_FULL = "TRANSIT_FULL";         // Zone de transit pleine
    private static final String MSG_TRANSIT_AVAILABLE = "TRANSIT_AVAILABLE"; // Zone de transit disponible
    private static final String MSG_LOW_BATTERY = "LOW_BATTERY";           // Alerte batterie faible
    private boolean waitingForHelp = false; // Indique si le robot attend de l'aide

    // COORDINATEUR DE TÂCHES DÉCENTRALISÉ
    // Chaque robot a son propre coordinateur pour prendre des décisions autonomes
    private CoordinateurTaches taskCoordinator;

    // COORDONNÉES DES ZONES DE TRANSIT DANS LA GRILLE
    // Ces positions définissent où se trouvent les zones de transit pour optimiser les livraisons
    int[][] transitZones = {{12, 10}, {12, 9}, {9, 10}, {9, 9}};

    // COORDONNÉES DES STATIONS DE RECHARGE DISTRIBUÉES SUR LA GRILLE
    // Ces positions permettent aux robots de recharger leur batterie stratégiquement
    int[][] chargingStations = {
        // Stations originales près des zones de transit pour un accès rapide
        {11, 10}, {13, 9}, {8, 10}, {10, 9},
        // Coins et bords de la grille pour couvrir toute la zone
        {2, 2}, {17, 2}, {2, 17}, {17, 17},
        // Milieu des bords pour un accès depuis les zones périphériques
        {10, 2}, {2, 10}, {17, 10}, {10, 17},
        // Zones stratégiques intermédiaires pour réduire les distances
        {5, 5}, {15, 5}, {5, 15}, {15, 15}
    };

    // COORDONNÉES DE LA ZONE D'ATTENTE CENTRALE
    // Cette zone sert de point de rassemblement pour les robots libres
    private static final int CENTRAL_AREA_X = 10;  // Position X de la zone centrale
    private static final int CENTRAL_AREA_Y = 12;  // Position Y de la zone centrale
    private boolean isCharging = false;             // Indique si le robot est en train de charger

    // CONSTANTES DE GESTION DE BATTERIE SIMPLIFIÉES
    // Valeurs réalistes pour un projet étudiant
    private static final double MAX_BATTERY = 100.0;                    // Capacité maximale de la batterie
    private static final double CRITICAL_BATTERY_THRESHOLD = 20.0;      // Seuil critique (20%) - plus sécuritaire
    private static final double MOVE_BATTERY_COST = 1.0;                // Coût énergétique par mouvement (1%) - plus réaliste
    private static final double PICKUP_BATTERY_COST = 1.0;              // Coût énergétique pour prendre un colis (1%) - minimisé
    private static final double DEPOSIT_BATTERY_COST = 1.0;             // Coût énergétique pour déposer un colis (1%) - minimisé
    private static final double CHARGING_RATE = 10.0;                   // Vitesse de recharge (10%/cycle) - très rapide pour minimiser les temps d'arrêt
    private static final double TARGET_BATTERY_LEVEL = 80.0;            // Niveau cible de charge (80%) - charge partielle pour efficacité

    /**
     * CONSTRUCTEUR POUR CRÉER UN NOUVEAU ROBOT DE TRANSIT DÉCENTRALISÉ
     *
     * Ce constructeur initialise un robot de transit avec toutes les capacités
     * nécessaires pour fonctionner dans le système d'enchères décentralisé.
     * Il configure le robot pour qu'il puisse :
     *
     * - Utiliser les zones de transit pour optimiser les livraisons
     * - Communiquer avec les autres robots via des messages
     * - Prendre des décisions autonomes grâce à son coordinateur personnel
     * - Gérer sa batterie de manière intelligente
     * - Participer aux enchères pour les tâches disponibles
     *
     * Le processus d'initialisation fonctionne ainsi :
     * Étape 1 : Appeler le constructeur parent pour les fonctionnalités de base
     * Étape 2 : Initialiser l'état du robot comme libre et disponible
     * Étape 3 : Configurer la batterie à son niveau maximum
     * Étape 4 : Créer le coordinateur de tâches personnel du robot
     * Étape 5 : Enregistrer l'initialisation dans les logs du système
     *
     * @param name Nom unique du robot pour l'identification
     * @param field Paramètre de configuration du champ de vision
     * @param debug Niveau de débogage pour les messages
     * @param pos Position initiale du robot [x, y]
     * @param color Couleur d'affichage du robot
     * @param rows Nombre de lignes de la grille
     * @param columns Nombre de colonnes de la grille
     * @param env Environnement de simulation dans lequel évolue le robot
     * @param seed Graine pour la génération de nombres aléatoires
     */
    public MyTransitRobot(String name, int field, int debug, int[] pos, Color color, int rows, int columns, ColorGridEnvironment env, long seed) {
        // Étape 1 : Appeler le constructeur parent pour hériter des fonctionnalités de base
        super(name, field, debug, pos, color, rows, columns, env, seed);

        // Étape 2 : Initialiser l'état du robot comme libre et prêt à travailler
        this.transitState = TransitState.FREE;

        // Étape 3 : Configurer la batterie à son niveau maximum pour commencer
        this.batteryLevel = MAX_BATTERY;

        // Étape 4 : Créer notre propre instance de coordinateur de tâches décentralisé
        // Chaque robot a son coordinateur personnel pour prendre des décisions autonomes
        this.taskCoordinator = new CoordinateurTaches(this, env);

        // Étape 5 : Enregistrer l'initialisation réussie dans les logs
        LogManager.getInstance().logAction(name, "Initialisé avec un coordinateur de tâches décentralisé");
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE COORDINATEUR DE TÂCHES DE CE ROBOT
     *
     * Cette méthode donne accès au coordinateur de tâches personnel du robot.
     * Le coordinateur est responsable de :
     * - Analyser les tâches disponibles
     * - Calculer les offres d'enchères
     * - Gérer les connaissances sur l'environnement
     * - Optimiser les décisions de livraison
     *
     * @return Le coordinateur de tâches associé à ce robot
     */
    public CoordinateurTaches getTaskCoordinator() {
        return taskCoordinator;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE COORDINATEUR DE TÂCHES (VERSION FRANÇAISE)
     *
     * Cette méthode est identique à getTaskCoordinator() mais avec un nom français
     * pour maintenir la cohérence avec le style de codage du projet.
     * Elle permet d'accéder au coordinateur de tâches du robot.
     *
     * @return Le coordinateur de tâches associé à ce robot
     */
    public CoordinateurTaches getCoordinateurTaches() {
        return taskCoordinator;
    }

    /**
     * MÉTHODE POUR DIFFUSER UN MESSAGE À TOUS LES ROBOTS
     *
     * Cette méthode fait partie du système de communication décentralisé.
     * Elle permet à un robot d'envoyer un message à tous les autres robots
     * de l'environnement. C'est utilisé pour :
     *
     * - Annoncer de nouvelles tâches disponibles
     * - Partager des informations sur l'état des zones de transit
     * - Diffuser des mises à jour d'efficacité
     * - Informer de l'attribution ou de la completion de tâches
     *
     * Le processus fonctionne ainsi :
     * 1. Récupérer la liste de tous les robots dans l'environnement
     * 2. Parcourir chaque robot
     * 3. Vérifier que c'est un MyTransitRobot (et pas nous-même)
     * 4. Envoyer le message à ce robot
     *
     * @param message Le message à diffuser à tous les robots
     */
    public void broadcastMessage(Message message) {
        // Étape 1 : Récupérer tous les robots de l'environnement
        List<Robot> tousLesRobots = environnement.getRobot();

        // Compteur pour suivre combien de robots ont reçu le message
        int robotsContactes = 0;

        // Étape 2 : Parcourir chaque robot dans l'environnement
        for (Robot robot : tousLesRobots) {
            // Étape 3 : Vérifier que c'est un autre MyTransitRobot (pas nous-même)
            if (robot != this && robot instanceof MyTransitRobot) {
                // Étape 4 : Envoyer le message à ce robot
                ((MyTransitRobot)robot).handleMessage(message);
                robotsContactes++;
            }
        }

        // Enregistrer l'envoi dans les logs pour le débogage
        LogManager.getInstance().logCoordination(this.getName(),
            "Message diffusé à " + robotsContactes + " robots de type " + message.getMessageType());
    }

    /**
     * MÉTHODE POUR ENVOYER UN MESSAGE DIRECT À UN ROBOT SPÉCIFIQUE
     *
     * Cette méthode permet d'envoyer un message à un robot particulier
     * plutôt qu'à tous les robots. C'est utilisé pour :
     *
     * - Répondre à une offre spécifique (acceptation/refus)
     * - Envoyer des informations privées à un robot
     * - Coordonner des actions entre deux robots spécifiques
     *
     * Le processus fonctionne ainsi :
     * 1. Récupérer la liste de tous les robots
     * 2. Chercher le robot avec l'ID spécifié
     * 3. Envoyer le message uniquement à ce robot
     * 4. Arrêter la recherche une fois le robot trouvé
     *
     * @param message Le message à envoyer
     * @param targetRobotId L'identifiant du robot destinataire
     */
    public void sendDirectMessage(Message message, String targetRobotId) {
        // Étape 1 : Récupérer tous les robots de l'environnement
        List<Robot> tousLesRobots = environnement.getRobot();

        // Étape 2 : Chercher le robot avec l'ID spécifié
        for (Robot robot : tousLesRobots) {
            // Vérifier si c'est le bon robot (même ID et de type MyTransitRobot)
            if (robot instanceof MyTransitRobot && robot.getName().equals(targetRobotId)) {
                // Étape 3 : Envoyer le message à ce robot spécifique
                ((MyTransitRobot)robot).handleMessage(message);

                // Enregistrer l'envoi dans les logs
                LogManager.getInstance().logCoordination(this.getName(),
                    "Message direct envoyé à " + targetRobotId + " de type " + message.getMessageType());

                // Étape 4 : Arrêter la recherche (robot trouvé)
                break;
            }
        }
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE NIVEAU ACTUEL DE LA BATTERIE
     *
     * Cette méthode simple retourne le niveau de batterie actuel du robot
     * sous forme de pourcentage (0-100%). Cette information est cruciale
     * pour la prise de décision du robot concernant :
     * - L'acceptation de nouvelles tâches
     * - La planification des trajets
     * - La nécessité de recharger
     * - La communication avec les autres robots
     *
     * @return Le niveau actuel de la batterie en pourcentage (0.0 à 100.0)
     */
    public double getBatteryLevel() {
        return batteryLevel;
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT A LIVRÉ SON COLIS
     *
     * Cette méthode vérifie l'état actuel du robot pour déterminer
     * s'il a terminé sa mission de livraison. Elle compare l'état
     * du robot avec l'énumération EtatRobot.LIVRE.
     *
     * Cette information est utilisée pour :
     * - Décider des actions suivantes du robot
     * - Mettre à jour les statistiques de livraison
     * - Informer les autres robots de la completion
     * - Déclencher le retour vers la zone centrale
     *
     * @return true si le robot a livré son colis, false sinon
     */
    public boolean hasDelivered() {
        return etatActuel == EtatRobot.LIVRE;
    }

    /**
     * MÉTHODE POUR TROUVER UNE ZONE DE TRANSIT DISPONIBLE (NON PLEINE)
     *
     * Cette méthode parcourt toutes les zones de transit connues pour
     * trouver la première qui peut encore accepter des colis. Elle est
     * essentielle pour l'optimisation des livraisons via zones de transit.
     *
     * Le processus de recherche fonctionne ainsi :
     * Étape 1 : Parcourir toutes les positions des zones de transit
     * Étape 2 : Pour chaque position, récupérer la cellule correspondante
     * Étape 3 : Vérifier que c'est bien une ColorCell avec une ColorTransitZone
     * Étape 4 : Tester si cette zone de transit n'est pas pleine
     * Étape 5 : Si trouvée, sauvegarder ses coordonnées et la retourner
     * Étape 6 : Si aucune zone disponible, retourner null
     *
     * @return La première zone de transit trouvée qui peut accepter un nouveau colis, ou null si aucune
     */
    private ColorTransitZone findTransitZoneNotFull() {
        // Étape 1 : Parcourir toutes les positions des zones de transit connues
        for (int[] positionZoneTransit : transitZones) {
            // Étape 2 : Récupérer la cellule à cette position dans la grille
            Cell celluleActuelle = environnement.getGrid()[positionZoneTransit[0]][positionZoneTransit[1]];

            // Étape 3 : Vérifier que c'est une cellule colorée avec une zone de transit
            if (celluleActuelle instanceof ColorCell && ((ColorCell)celluleActuelle).getContent() instanceof ColorTransitZone) {
                // Récupérer la zone de transit de cette cellule
                ColorTransitZone zoneDeTransit = (ColorTransitZone) ((ColorCell)celluleActuelle).getContent();

                // Étape 4 : Tester si cette zone n'est pas pleine
                if (!zoneDeTransit.isFull()) {
                    // Étape 5 : Sauvegarder les coordonnées de cette zone disponible
                    transitX = positionZoneTransit[0];
                    transitY = positionZoneTransit[1];
                    return zoneDeTransit;
                }
            }
        }
        // Étape 6 : Aucune zone de transit disponible trouvée
        return null;
    }

    /**
     * MÉTHODE POUR TROUVER UNE ZONE DE TRANSIT CONTENANT DES COLIS
     *
     * Cette méthode recherche une zone de transit qui contient au moins
     * un colis en attente de récupération. Elle permet aux robots de
     * trouver des colis déjà déposés par d'autres robots pour continuer
     * le processus de livraison décentralisé.
     *
     * Le processus de recherche fonctionne ainsi :
     * Étape 1 : Parcourir toutes les positions des zones de transit
     * Étape 2 : Pour chaque position, récupérer la cellule correspondante
     * Étape 3 : Vérifier que c'est bien une ColorCell avec une ColorTransitZone
     * Étape 4 : Tester si cette zone contient au moins un colis
     * Étape 5 : Si trouvée, sauvegarder ses coordonnées et la retourner
     * Étape 6 : Si aucune zone avec colis, retourner null
     *
     * @return La première zone de transit trouvée avec des colis, ou null si aucune
     */
    private ColorTransitZone findTransitZoneWithPackage() {
        // Étape 1 : Parcourir toutes les positions des zones de transit connues
        for (int[] positionZoneTransit : transitZones) {
            // Étape 2 : Récupérer la cellule à cette position dans la grille
            Cell celluleActuelle = environnement.getGrid()[positionZoneTransit[0]][positionZoneTransit[1]];

            // Étape 3 : Vérifier que c'est une cellule colorée avec une zone de transit
            if (celluleActuelle instanceof ColorCell && ((ColorCell)celluleActuelle).getContent() instanceof ColorTransitZone) {
                // Récupérer la zone de transit de cette cellule
                ColorTransitZone zoneDeTransit = (ColorTransitZone) ((ColorCell)celluleActuelle).getContent();

                // Étape 4 : Tester si cette zone contient au moins un colis
                if (zoneDeTransit.getPackages().size() > 0) {
                    // Étape 5 : Sauvegarder les coordonnées de cette zone avec colis
                    transitX = positionZoneTransit[0];
                    transitY = positionZoneTransit[1];
                    return zoneDeTransit;
                }
            }
        }
        // Étape 6 : Aucune zone de transit avec colis trouvée
        return null;
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI TOUTES LES ZONES DE TRANSIT SONT PLEINES
     *
     * Cette méthode est cruciale pour la gestion intelligente des zones de transit.
     * Elle permet au robot de savoir s'il peut encore utiliser une zone de transit
     * pour optimiser ses livraisons ou s'il doit passer en mode livraison directe.
     *
     * Le processus de vérification fonctionne ainsi :
     * 1. Parcourir toutes les positions des zones de transit connues
     * 2. Pour chaque position, récupérer la cellule correspondante
     * 3. Vérifier que c'est bien une ColorCell avec une ColorTransitZone
     * 4. Tester si cette zone de transit est pleine
     * 5. Si au moins une zone n'est pas pleine, retourner false
     * 6. Si toutes les zones sont pleines, retourner true
     *
     * Cette information est utilisée pour :
     * - Décider si le robot doit chercher une zone de transit
     * - Optimiser les stratégies de livraison
     * - Éviter les déplacements inutiles vers des zones saturées
     * - Informer les autres robots de la saturation du système
     *
     * @return true si toutes les zones de transit sont pleines, false sinon
     */
    private boolean transitZonesAreFull() {
        // Étape 1 : Parcourir toutes les positions des zones de transit
        for (int[] positionZoneTransit : transitZones) {
            // Étape 2 : Récupérer la cellule à cette position dans la grille
            Cell celluleActuelle = environnement.getGrid()[positionZoneTransit[0]][positionZoneTransit[1]];

            // Étape 3 : Vérifier que c'est une cellule colorée avec une zone de transit
            if (celluleActuelle instanceof ColorCell && celluleActuelle.getContent() instanceof ColorTransitZone) {
                // Récupérer la zone de transit de cette cellule
                ColorTransitZone zoneDeTransit = (ColorTransitZone) celluleActuelle.getContent();

                // Étape 4 : Tester si cette zone n'est PAS pleine
                if (!zoneDeTransit.isFull()) {
                    // Étape 5 : Si au moins une zone n'est pas pleine, retourner false
                    LogManager.getInstance().logTransit(getName(),
                        "Zone (" + positionZoneTransit[0] + "," + positionZoneTransit[1] + ")",
                        "zone de transit disponible trouvée");
                    return false;
                }
            }
        }

        // Étape 6 : Si on arrive ici, toutes les zones sont pleines
        LogManager.getInstance().logTransit(getName(), "Toutes zones",
            "toutes les zones de transit sont saturées");
        return true;
    }

    /**
     * MÉTHODE POUR DÉTERMINER S'IL EST AVANTAGEUX D'UTILISER UNE ZONE DE TRANSIT
     *
     * Cette méthode implémente un algorithme d'optimisation de trajet qui compare
     * deux stratégies de livraison : la livraison directe versus l'utilisation
     * d'une zone de transit comme point de relais intermédiaire.
     *
     * L'algorithme de décision fonctionne en plusieurs étapes :
     * 1. Calculer la distance directe de la position actuelle vers la destination
     * 2. Trouver une zone de transit disponible (non pleine)
     * 3. Calculer la distance totale via cette zone de transit
     * 4. Comparer les deux distances avec une marge de tolérance
     * 5. Décider quelle stratégie est la plus efficace
     *
     * Avantages de l'utilisation des zones de transit :
     * - Permet la coordination entre plusieurs robots
     * - Optimise l'utilisation de l'espace de stockage
     * - Facilite la gestion des flux de colis
     * - Réduit les conflits entre robots sur les longues distances
     *
     * La marge de tolérance de 30% permet d'accepter un léger détour
     * si cela apporte des bénéfices organisationnels au système global.
     *
     * @param destX Coordonnée X de la destination finale
     * @param destY Coordonnée Y de la destination finale
     * @return true si utiliser une zone de transit est avantageux, false sinon
     */
    private boolean isBetterToUseTransit(int destX, int destY) {
        // Étape 1 : Calculer la distance directe de notre position vers la destination
        double distanceDirecte = distanceTo(this.getX(), this.getY(), destX, destY);

        LogManager.getInstance().logTransit(getName(), "Calcul trajet",
            String.format("Distance directe vers destination (%d,%d) : %.1f", destX, destY, distanceDirecte));

        // Étape 2 : Chercher une zone de transit disponible (non pleine)
        ColorTransitZone zoneTransitDisponible = findTransitZoneNotFull();
        if (zoneTransitDisponible == null) {
            // Aucune zone de transit disponible, utilisation directe obligatoire
            LogManager.getInstance().logTransit(getName(), "Aucune zone",
                "aucune zone de transit disponible, livraison directe obligatoire");
            return false;
        }

        // Étape 3 : Calculer la distance totale via la zone de transit
        // Distance de notre position vers la zone de transit
        double distanceVersTransit = distanceTo(this.getX(), this.getY(), transitX, transitY);

        // Distance de la zone de transit vers la destination finale
        double distanceTransitVersDestination = distanceTo(transitX, transitY, destX, destY);

        // Distance totale en passant par la zone de transit
        double distanceTotaleViaTransit = distanceVersTransit + distanceTransitVersDestination;

        LogManager.getInstance().logTransit(getName(),
            "Zone (" + transitX + "," + transitY + ")",
            String.format("Distance via transit : %.1f (%.1f + %.1f)",
                distanceTotaleViaTransit, distanceVersTransit, distanceTransitVersDestination));

        // Étape 4 : Comparer avec une marge de tolérance de 30%
        // Cette marge permet d'accepter un léger détour pour les bénéfices organisationnels
        double seuilAcceptable = distanceDirecte * 1.3;
        boolean utilisationTransitAvantageuse = distanceTotaleViaTransit <= seuilAcceptable;

        // Étape 5 : Enregistrer la décision et retourner le résultat
        if (utilisationTransitAvantageuse) {
            LogManager.getInstance().logTransit(getName(), "Décision",
                "utilisation de la zone de transit recommandée (efficace)");
        } else {
            LogManager.getInstance().logTransit(getName(), "Décision",
                "livraison directe recommandée (plus courte)");
        }

        return utilisationTransitAvantageuse;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE COLIS ACTUELLEMENT TRANSPORTÉ
     *
     * Cette méthode retourne une référence vers le colis que le robot
     * transporte actuellement. Elle est utilisée par d'autres composants
     * du système pour vérifier si le robot porte un colis et accéder
     * aux informations de ce colis (destination, priorité, etc.).
     *
     * @return Le colis transporté, ou null si le robot ne porte rien
     */
    public ColorPackage getCarriedPackage() {
        return colisTransporte;
    }

    /**
     * MÉTHODE POUR DÉFINIR LE COLIS À TRANSPORTER
     *
     * Cette méthode permet d'assigner un colis au robot. Elle est utilisée
     * quand le robot récupère un colis depuis une zone de départ ou une
     * zone de transit. Le colis devient alors la responsabilité du robot
     * jusqu'à sa livraison.
     *
     * @param pack Le colis à transporter
     */
    public void setCarriedPackage(ColorPackage pack) {
        this.colisTransporte = pack;
    }

    /**
     * MÉTHODE PRINCIPALE POUR LA LOGIQUE DE MOUVEMENT DU ROBOT DE TRANSIT
     *
     * Cette méthode est le cœur du comportement autonome du robot. Elle est
     * appelée à chaque cycle de simulation et détermine les actions du robot
     * selon son état actuel et les conditions de l'environnement.
     *
     * La logique suit cette hiérarchie de priorités :
     * 1. Gestion des situations de blocage (déblocage)
     * 2. Retour vers la zone centrale après livraison
     * 3. Gestion ultra-optimisée de la batterie
     * 4. Recherche et récupération de colis
     * 5. Transport et livraison des colis
     *
     * Le robot prend des décisions autonomes basées sur :
     * - Son état actuel (libre, transport, retour, etc.)
     * - Son niveau de batterie
     * - La disponibilité des zones de transit
     * - Les colis disponibles dans l'environnement
     * - Les informations de son coordinateur de tâches
     */
    @Override
    public void step() {
        // PRIORITÉ 1 : Vérifier si le robot est bloqué et essayer de se débloquer
        if (isStuck()) {
            tryToUnstuck();
            return;
        }

        // PRIORITÉ 2 : Gérer le retour vers la zone centrale après livraison
        if (transitState == TransitState.RETURNING_TO_CENTER) {
            // Vérifier si on a atteint la zone centrale
            if (isNearCentralArea()) {
                // Étape 1 : Le robot a atteint la zone centrale, le remettre en état libre
                transitState = TransitState.FREE;
                etatActuel = EtatRobot.LIBRE;
                LogManager.getInstance().logAction(getName(), "est arrivé à la zone d'attente centrale et est prêt pour une nouvelle tâche");
            } else {
                // Étape 2 : Continuer à se déplacer vers la zone centrale
                moveTowardsCentralArea();
            }
            return;
        }

        // GESTION SIMPLIFIÉE DE LA BATTERIE
        if (isCharging) {
            // Charger jusqu'à 80% pour un bon équilibre temps/autonomie
            if (batteryLevel < TARGET_BATTERY_LEVEL) {
                chargeBattery();
                return;
            } else {
                // Arrêter la charge une fois le niveau cible atteint
                isCharging = false;
                LogManager.getInstance().logBattery(getName(), batteryLevel, "charge terminée, retour au travail");
            }
        }

        // Charger seulement si critique - jamais de charge opportuniste
        // Cela maximise le temps que les robots passent sur les tâches
        if (batteryLevel <= CRITICAL_BATTERY_THRESHOLD) {
            if (isNearChargingStation()) {
                isCharging = true;
                LogManager.getInstance().logBattery(getName(), batteryLevel, "a commencé à charger sa batterie");
                chargeBattery(); // Commencer à charger immédiatement
                return;
            } else {
                // Se déplacer vers une station de charge seulement si critique
                int[] nearestCS = findNearestChargingStation();
                if (nearestCS != null) {
                    moveOneStepTo(nearestCS[0], nearestCS[1]);
                    consumeBatteryForMovement();
                    return;
                }
            }
        }

        // Le robot est libre et cherche un colis
        if (etatActuel == EtatRobot.LIBRE) {
            // D'abord vérifier s'il y a des colis dans les zones de transit
            ColorTransitZone transitWithPackage = findTransitZoneWithPackage();

            if (transitWithPackage != null) {
                // Il y a un colis dans une zone de transit, aller le chercher
                if (isAdjacentTo(transitX, transitY)) {
                    // Nous sommes à côté de la zone de transit, récupérer le colis
                    List<ColorPackage> packages = transitWithPackage.getPackages();
                    if (!packages.isEmpty()) {
                        colisTransporte = packages.get(0);
                        transitWithPackage.removePackage(colisTransporte);
                        consumeBatteryForPickup();
                        momentDepart = System.currentTimeMillis();

                        int[] goalPos = DESTINATIONS.get(colisTransporte.getDestinationGoalId());
                        if (goalPos != null) {
                            destinationX = goalPos[0];
                            destinationY = goalPos[1];
                            etatActuel = EtatRobot.TRANSPORT;
                            transitState = TransitState.GOING_TO_GOAL;
                        }

                        LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                            "a pris un colis de la zone de transit (" + transitX + "," + transitY + ") pour la destination " + colisTransporte.getDestinationGoalId());

                        // Diffuser la mise à jour du statut de la zone de transit
                        TransitZoneStatusMessage message = new TransitZoneStatusMessage(this, transitX, transitY, false);
                        broadcastMessage(message);
                    }
                } else {
                    // Se déplacer vers la zone de transit
                    moveOneStepTo(transitX, transitY);
                }
            } else {
                // Vérifier les zones de départ pour des colis
                ColorStartZone startZone = findStartZoneWithPackage();
                if (startZone == null) {
                    // Pas de colis dans les zones de départ, vérifier si on doit chercher des tâches
                    // Utiliser notre coordinateur de tâches pour trouver et enchérir sur des tâches
                    taskCoordinator.findBestTaskForRobot();
                    return;
                }

                if (isAdjacentTo(startZone.getX(), startZone.getY())) {
                    // Nous sommes à côté de la zone de départ, récupérer le colis
                    if (!startZone.getPackages().isEmpty()) {
                        colisTransporte = startZone.getPackages().get(0);
                        startZone.removePackage(colisTransporte);
                        consumeBatteryForPickup();
                        momentDepart = System.currentTimeMillis();

                        int[] goalPos = DESTINATIONS.get(colisTransporte.getDestinationGoalId());
                        if (goalPos != null) {
                            destinationX = goalPos[0];
                            destinationY = goalPos[1];

                            // Créer une tâche pour ce colis dans notre coordinateur
                            CoordinateurTaches.Task task = taskCoordinator.createTask(
                                colisTransporte, startZone, DESTINATIONS);

                            // Utiliser notre TaskCoordinator local pour décider d'utiliser une zone de transit
                            ColorTransitZone tz = findTransitZoneNotFull();

                            if (tz != null && taskCoordinator.shouldUseTransitZone(this, destinationX, destinationY, transitX, transitY)) {
                                // Utiliser la zone de transit basé sur la décision du coordinateur
                                etatActuel = EtatRobot.TRANSPORT;
                                transitState = TransitState.GOING_TO_TRANSIT;
                                LogManager.getInstance().logCoordination(getName(), "Prise du colis depuis " + colisTransporte.getStartZone() +
                                                 " et utilisation de la zone de transit (" + transitX + "," + transitY + ")");
                            } else {
                                // Livraison directe
                                etatActuel = EtatRobot.TRANSPORT;
                                transitState = TransitState.GOING_TO_GOAL;
                                LogManager.getInstance().logCoordination(getName(), "Prise du colis depuis " + colisTransporte.getStartZone() +
                                                 " pour livraison directe à la destination " + colisTransporte.getDestinationGoalId());
                            }
                        }
                    }
                } else {
                    // Se déplacer vers la zone de départ
                    moveOneStepTo(startZone.getX(), startZone.getY());
                }
            }
        } else if (etatActuel == EtatRobot.TRANSPORT) {
            // Le robot transporte un colis
            if (transitState == TransitState.GOING_TO_TRANSIT) {
                // Se dirige vers une zone de transit
                if (isAdjacentTo(transitX, transitY)) {
                    // Nous sommes à côté de la zone de transit, déposer le colis
                    Cell c = environnement.getGrid()[transitX][transitY];
                    if (c instanceof ColorCell && c.getContent() instanceof ColorTransitZone) {
                        ColorTransitZone tz = (ColorTransitZone) c.getContent();
                        if (!tz.isFull()) {
                            tz.addPackage(colisTransporte);
                            consumeBatteryForDeposit();
                            LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                                "a déposé un colis dans la zone de transit (" + transitX + "," + transitY + ")");

                            // Vérifier si la zone de transit est maintenant pleine
                            boolean isFull = tz.isFull();

                            // Diffuser la mise à jour du statut de la zone de transit
                            TransitZoneStatusMessage message = new TransitZoneStatusMessage(this, transitX, transitY, isFull);
                            broadcastMessage(message);

                            colisTransporte = null;
                            etatActuel = EtatRobot.LIBRE;
                            transitState = TransitState.FREE;
                        }
                    }
                } else {
                    // Se déplacer vers la zone de transit
                    moveOneStepTo(transitX, transitY);
                }
            } else if (transitState == TransitState.GOING_TO_GOAL) {
                // Se dirige vers la destination finale
                if ((this.getX() == destinationX) && (this.getY() == destinationY)) {
                    // Nous avons atteint la destination
                    colisTransporte.setState(PackageState.ARRIVED);
                    consumeBatteryForDeposit();
                    MySimFactory.deliveredCount++;

                    momentArrivee = System.currentTimeMillis();
                    long deliveryTime = momentArrivee - momentDepart;
                    double batteryUsed = MAX_BATTERY - batteryLevel;

                    LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                        "a livré le colis à destination en " + (deliveryTime/1000) + " secondes avec " +
                        String.format("%.1f", batteryUsed) + "% de batterie consommée");

                    // Mettre à jour notre coordinateur de tâches local avec les statistiques de livraison
                    taskCoordinator.updateRobotEfficiency(this, deliveryTime, batteryUsed);

                    // Diffuser la completion de tâche à tous les robots
                    TaskCompletedMessage message = new TaskCompletedMessage(
                        this, "Task-" + colisTransporte.getId(), deliveryTime, batteryUsed);
                    broadcastMessage(message);

                    // Mettre le colis transporté à null pour s'assurer qu'il n'interagit avec rien
                    colisTransporte = null;
                    // Mettre isCharging à false pour s'assurer qu'il n'essaie pas de charger
                    isCharging = false;

                    // Au lieu de supprimer le robot, le faire retourner vers la zone centrale
                    transitState = TransitState.RETURNING_TO_CENTER;
                    LogManager.getInstance().logAction(getName(), "a livré un colis et retourne à la zone d'attente centrale");
                } else {
                    // Vérifier si nous avons assez de batterie pour atteindre l'objectif
                    if (batteryLevel < CRITICAL_BATTERY_THRESHOLD || !canReachDestination(destinationX, destinationY)) {
                        // Pas assez de batterie, trouver une station de charge
                        if (isNearChargingStation()) {
                            isCharging = true;
                            LogManager.getInstance().logBattery(getName(), batteryLevel, "a besoin de recharger avant de continuer vers la destination");
                            return;
                        } else {
                            // Se déplacer vers la station de charge la plus proche
                            int[] nearestCS = findNearestChargingStation();
                            if (nearestCS != null) {
                                LogManager.getInstance().logBattery(getName(), batteryLevel, "se dirige vers une station de recharge car batterie insuffisante pour atteindre la destination");
                                moveOneStepTo(nearestCS[0], nearestCS[1]);
                                return;
                            }
                        }
                    }
                    // Se déplacer vers l'objectif
                    moveOneStepTo(destinationX, destinationY);
                    consumeBatteryForMovement();
                }
            }
        }
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT EST PROCHE D'UNE ZONE DE SORTIE
     *
     * Cette méthode détermine si le robot se trouve dans la proximité immédiate
     * d'une zone de sortie de l'entrepôt. Les zones de sortie sont généralement
     * situées en bordure de la grille, typiquement en haut (y=0).
     *
     * Le processus de vérification fonctionne ainsi :
     * 1. Récupérer la position Y actuelle du robot
     * 2. Comparer cette position avec les coordonnées des zones de sortie
     * 3. Considérer une marge de proximité (1 cellule) pour anticiper
     * 4. Retourner true si le robot est dans cette zone de proximité
     *
     * Cette information est utilisée pour :
     * - Optimiser les trajets de sortie après livraison
     * - Éviter les embouteillages près des sorties
     * - Planifier les retours vers les zones centrales
     * - Gérer les priorités de passage près des sorties
     *
     * La marge de proximité (y <= 1) permet au robot d'anticiper
     * son arrivée près de la sortie et de préparer ses actions suivantes.
     *
     * @return true si le robot est proche d'une zone de sortie, false sinon
     */
    private boolean isNearExit() {
        // Étape 1 : Récupérer la position Y actuelle du robot
        int positionYActuelle = this.getY();

        // Étape 2 : Vérifier si nous sommes dans la zone de proximité des sorties
        // Les sorties sont situées en haut de la grille (y=0)
        // On considère y=0 et y=1 comme "proche de la sortie"
        boolean procheDesSorties = positionYActuelle <= 1;

        // Étape 3 : Enregistrer l'information pour le débogage
        if (procheDesSorties) {
            LogManager.getInstance().logAction(getName(),
                "est proche des zones de sortie (position Y=" + positionYActuelle + ")");
        }

        // Étape 4 : Retourner le résultat de la vérification
        return procheDesSorties;
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT EST PROCHE DE LA ZONE D'ATTENTE CENTRALE
     *
     * Cette méthode détermine si le robot se trouve dans la proximité immédiate
     * de la zone d'attente centrale où les robots libres se rassemblent.
     * Elle utilise la distance de Manhattan pour calculer la proximité.
     *
     * Le processus de vérification fonctionne ainsi :
     * Étape 1 : Calculer la distance de Manhattan vers la zone centrale
     * Étape 2 : Comparer cette distance avec le seuil de proximité (1 cellule)
     * Étape 3 : Retourner true si le robot est dans cette zone de proximité
     *
     * @return true si le robot est proche de la zone centrale, false sinon
     */
    private boolean isNearCentralArea() {
        // Étape 1 : Calculer la distance de Manhattan vers la zone centrale
        int distanceManhattan = Math.abs(this.getX() - CENTRAL_AREA_X) + Math.abs(this.getY() - CENTRAL_AREA_Y);

        // Étape 2 : Vérifier si nous sommes à 1 cellule ou moins de la zone centrale
        return distanceManhattan <= 1;
    }

    /**
     * MÉTHODE POUR DÉPLACER LE ROBOT VERS LA ZONE D'ATTENTE CENTRALE
     *
     * Cette méthode fait avancer le robot d'un pas vers la zone d'attente centrale.
     * Elle est utilisée quand le robot a terminé une livraison et doit retourner
     * à la zone de rassemblement pour attendre de nouvelles tâches.
     *
     * Le processus fonctionne ainsi :
     * Étape 1 : Calculer et effectuer un mouvement vers la zone centrale
     * Étape 2 : Consommer la batterie pour ce mouvement
     * Étape 3 : Enregistrer l'action dans les logs pour le suivi
     */
    private void moveTowardsCentralArea() {
        // Étape 1 : Se déplacer d'un pas vers la zone centrale
        moveOneStepTo(CENTRAL_AREA_X, CENTRAL_AREA_Y);

        // Étape 2 : Consommer la batterie pour ce mouvement
        consumeBatteryForMovement();

        // Étape 3 : Enregistrer l'action dans les logs
        LogManager.getInstance().logTransit(getName(), "Zone centrale", "se dirige vers la zone d'attente centrale");
    }

    // VARIABLES POUR LA DÉTECTION DE BLOCAGE DU ROBOT
    // Ces variables permettent de détecter quand un robot reste immobile trop longtemps
    private int stuckCounter = 0;       // Compteur d'étapes consécutives sans mouvement
    private int lastX = -1;             // Dernière position X enregistrée
    private int lastY = -1;             // Dernière position Y enregistrée

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT EST BLOQUÉ
     *
     * Cette méthode détecte si le robot n'a pas bougé depuis plusieurs cycles
     * de simulation, ce qui indique qu'il est probablement bloqué par un
     * obstacle ou dans une situation de conflit avec d'autres robots.
     *
     * Le processus de détection fonctionne ainsi :
     * Étape 1 : Comparer la position actuelle avec la position précédente
     * Étape 2 : Si identique, incrémenter le compteur de blocage
     * Étape 3 : Si différente, remettre le compteur à zéro
     * Étape 4 : Considérer le robot bloqué après 5 cycles sans mouvement
     * Étape 5 : Sauvegarder la position actuelle pour la prochaine vérification
     *
     * @return true si le robot est considéré comme bloqué, false sinon
     */
    private boolean isStuck() {
        // Étape 1 : Comparer la position actuelle avec la position précédente
        if (lastX == this.getX() && lastY == this.getY()) {
            // Étape 2 : Position identique, incrémenter le compteur de blocage
            stuckCounter++;
            // Étape 4 : Considérer bloqué après 5 étapes sans mouvement
            return stuckCounter > 5;
        } else {
            // Étape 3 : Position différente, remettre le compteur à zéro
            stuckCounter = 0;
            // Étape 5 : Sauvegarder la nouvelle position
            lastX = this.getX();
            lastY = this.getY();
            return false;
        }
    }

    /**
     * MÉTHODE POUR ESSAYER DE DÉBLOQUER LE ROBOT
     *
     * Cette méthode tente de sortir le robot d'une situation de blocage
     * en effectuant un mouvement aléatoire. C'est une stratégie simple
     * mais efficace pour résoudre les conflits temporaires.
     *
     * Le processus de déblocage fonctionne ainsi :
     * Étape 1 : Choisir une orientation aléatoire
     * Étape 2 : Vérifier si le mouvement vers l'avant est possible
     * Étape 3 : Si possible, effectuer le mouvement
     * Étape 4 : Consommer la batterie pour ce mouvement
     * Étape 5 : Enregistrer la tentative de déblocage dans les logs
     */
    private void tryToUnstuck() {
        // Étape 1 : Choisir une orientation aléatoire pour essayer de se débloquer
        randomOrientation();

        // Étape 2 : Vérifier si le mouvement vers l'avant est libre
        if (freeForward()) {
            // Étape 3 : Effectuer le mouvement vers l'avant
            moveForward();

            // Étape 4 : Consommer la batterie pour ce mouvement
            consumeBatteryForMovement();

            // Étape 5 : Enregistrer la tentative de déblocage
            LogManager.getInstance().logAction(getName(), "essaie de se débloquer avec un mouvement aléatoire");
        }
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT EST PROCHE D'UNE STATION DE RECHARGE
     *
     * Cette méthode détermine si le robot se trouve dans la proximité immédiate
     * d'une station de recharge. Elle parcourt toutes les stations connues et
     * calcule la distance de Manhattan pour chacune.
     *
     * Le processus de vérification fonctionne ainsi :
     * Étape 1 : Parcourir toutes les positions des stations de recharge
     * Étape 2 : Pour chaque station, calculer la distance de Manhattan
     * Étape 3 : Si une station est à distance 1 ou moins, retourner true
     * Étape 4 : Si aucune station proche, retourner false
     *
     * @return true si le robot est proche d'une station de recharge, false sinon
     */
    private boolean isNearChargingStation() {
        // Étape 1 : Parcourir toutes les positions des stations de recharge
        for (int[] positionStation : chargingStations) {
            // Étape 2 : Calculer la distance de Manhattan vers cette station
            int distanceManhattan = Math.abs(this.getX() - positionStation[0]) + Math.abs(this.getY() - positionStation[1]);

            // Étape 3 : Si la station est à distance 1 ou moins, on est proche
            if (distanceManhattan <= 1) {
                return true;
            }
        }
        // Étape 4 : Aucune station de recharge proche trouvée
        return false;
    }

    /**
     * MÉTHODE POUR TROUVER LA STATION DE RECHARGE LA PLUS PROCHE
     *
     * Cette méthode parcourt toutes les stations de recharge disponibles
     * et détermine laquelle est la plus proche du robot en utilisant
     * la distance euclidienne pour une précision optimale.
     *
     * Le processus de recherche fonctionne ainsi :
     * Étape 1 : Initialiser les variables de recherche
     * Étape 2 : Parcourir toutes les stations de recharge
     * Étape 3 : Pour chaque station, calculer la distance euclidienne
     * Étape 4 : Garder trace de la station la plus proche
     * Étape 5 : Retourner les coordonnées de la station optimale
     *
     * @return Les coordonnées [x, y] de la station de recharge la plus proche, ou null si aucune
     */
    private int[] findNearestChargingStation() {
        // Étape 1 : Initialiser les variables de recherche
        int[] stationLaPlusProche = null;
        double distanceMinimale = Double.MAX_VALUE;

        // Étape 2 : Parcourir toutes les stations de recharge disponibles
        for (int[] positionStation : chargingStations) {
            // Étape 3 : Calculer la distance euclidienne vers cette station
            double distanceVersStation = distanceTo(this.getX(), this.getY(), positionStation[0], positionStation[1]);

            // Étape 4 : Si cette station est plus proche, la sauvegarder
            if (distanceVersStation < distanceMinimale) {
                distanceMinimale = distanceVersStation;
                stationLaPlusProche = positionStation;
            }
        }

        // Étape 5 : Retourner les coordonnées de la station optimale
        return stationLaPlusProche;
    }

    /**
     * MÉTHODE POUR CHARGER LA BATTERIE DU ROBOT
     *
     * Cette méthode gère le processus de recharge de la batterie quand le robot
     * est positionné près d'une station de recharge. Elle met à jour le niveau
     * de batterie, gère l'arrêt automatique quand la batterie est pleine, et
     * maintient les logs de recharge pour le suivi.
     *
     * Le processus de recharge fonctionne ainsi :
     * Étape 1 : Vérifier que le robot est effectivement en train de charger
     * Étape 2 : Sauvegarder le niveau de batterie avant recharge
     * Étape 3 : Augmenter le niveau de batterie selon le taux de recharge
     * Étape 4 : Vérifier si la batterie a atteint son maximum
     * Étape 5 : Arrêter la recharge si la batterie est pleine
     * Étape 6 : Enregistrer l'état de recharge dans les logs
     * Étape 7 : Mettre à jour la couleur du robot selon le niveau de batterie
     */
    private void chargeBattery() {
        // Étape 1 : Vérifier que le robot est effectivement en mode recharge
        if (isCharging) {
            // Étape 2 : Sauvegarder le niveau de batterie avant recharge
            double niveauAvantRecharge = batteryLevel;

            // Étape 3 : Augmenter le niveau de batterie selon le taux de recharge
            batteryLevel += CHARGING_RATE;

            // Étape 4 : Vérifier si la batterie a atteint ou dépassé son maximum
            if (batteryLevel >= MAX_BATTERY) {
                // Étape 5 : Limiter la batterie au maximum et arrêter la recharge
                batteryLevel = MAX_BATTERY;
                isCharging = false;
                LogManager.getInstance().logCharging(getName(), niveauAvantRecharge, batteryLevel, "a terminé de charger sa batterie");
            } else {
                // Étape 6 : Enregistrer l'état de recharge en cours
                LogManager.getInstance().logCharging(getName(), niveauAvantRecharge, batteryLevel, "est en train de charger");
            }

            // Étape 7 : Mettre à jour la couleur du robot selon le nouveau niveau de batterie
            updateRobotColor();
        }
    }

    /**
     * MÉTHODE POUR CONSOMMER DE LA BATTERIE LORS DU MOUVEMENT
     *
     * Cette méthode gère la consommation d'énergie quand le robot se déplace.
     * Elle met à jour le niveau de batterie, enregistre la consommation dans
     * les logs, et déclenche des alertes si la batterie devient faible.
     *
     * Le processus de consommation fonctionne ainsi :
     * Étape 1 : Sauvegarder le niveau de batterie avant consommation
     * Étape 2 : Déduire le coût énergétique du mouvement
     * Étape 3 : Enregistrer la consommation détaillée dans les logs
     * Étape 4 : Vérifier si la batterie est devenue faible
     * Étape 5 : Diffuser une alerte si nécessaire
     * Étape 6 : Mettre à jour la couleur visuelle du robot
     */
    public void consumeBatteryForMovement() {
        // Étape 1 : Sauvegarder le niveau de batterie avant consommation
        double niveauAvantConsommation = batteryLevel;

        // Étape 2 : Déduire le coût énergétique du mouvement
        batteryLevel -= MOVE_BATTERY_COST;

        // Étape 3 : Enregistrer la consommation détaillée dans les logs
        LogManager.getInstance().logBattery(getName(), batteryLevel,
            String.format("Mouvement: -%.1f%% (%.1f%% → %.1f%%)", MOVE_BATTERY_COST, niveauAvantConsommation, batteryLevel));

        // Étape 4 : Vérifier si la batterie est devenue critique (simplification)
        if (batteryLevel < CRITICAL_BATTERY_THRESHOLD && !isCharging) {
            // Étape 5 : Enregistrer l'alerte et diffuser aux autres robots
            LogManager.getInstance().logBattery(getName(), batteryLevel, "niveau de batterie critique");
            broadcastLowBatteryMessage();
        }

        // Étape 6 : Mettre à jour la couleur du robot selon le niveau de batterie
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR CONSOMMER DE LA BATTERIE LORS DE LA RÉCUPÉRATION D'UN COLIS
     *
     * Cette méthode gère la consommation d'énergie quand le robot récupère
     * un colis depuis une zone de départ ou une zone de transit. L'opération
     * de récupération nécessite de l'énergie pour les mécanismes de préhension.
     *
     * Le processus fonctionne ainsi :
     * Étape 1 : Déduire le coût énergétique de la récupération
     * Étape 2 : La mise à jour de couleur sera gérée par d'autres méthodes
     */
    public void consumeBatteryForPickup() {
        // Étape 1 : Déduire le coût énergétique de la récupération de colis
        batteryLevel -= PICKUP_BATTERY_COST;

        // Note : La mise à jour de couleur et les logs sont gérés par d'autres méthodes
        // pour éviter la duplication de code et maintenir la cohérence
    }

    /**
     * MÉTHODE POUR CONSOMMER DE LA BATTERIE LORS DU DÉPÔT D'UN COLIS
     *
     * Cette méthode gère la consommation d'énergie quand le robot dépose
     * un colis à sa destination finale ou dans une zone de transit.
     * L'opération de dépôt nécessite de l'énergie pour les mécanismes de libération.
     *
     * Le processus de consommation fonctionne ainsi :
     * Étape 1 : Sauvegarder le niveau de batterie avant consommation
     * Étape 2 : Déduire le coût énergétique du dépôt
     * Étape 3 : Enregistrer la consommation détaillée dans les logs
     * Étape 4 : Mettre à jour la couleur visuelle du robot
     */
    public void consumeBatteryForDeposit() {
        // Étape 1 : Sauvegarder le niveau de batterie avant consommation
        double niveauAvantConsommation = batteryLevel;

        // Étape 2 : Déduire le coût énergétique du dépôt
        batteryLevel -= DEPOSIT_BATTERY_COST;

        // Étape 3 : Enregistrer la consommation détaillée dans les logs
        LogManager.getInstance().logBattery(getName(), batteryLevel,
            String.format("Dépôt: -%.1f%% (%.1f%% → %.1f%%)", DEPOSIT_BATTERY_COST, niveauAvantConsommation, batteryLevel));

        // Étape 4 : Mettre à jour la couleur du robot selon le niveau de batterie
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR METTRE À JOUR LA COULEUR DU ROBOT SELON LE NIVEAU DE BATTERIE
     *
     * Cette méthode change la couleur d'affichage du robot pour refléter visuellement
     * son niveau de batterie actuel. Cela permet aux utilisateurs et aux autres robots
     * de rapidement identifier l'état énergétique de chaque robot dans la simulation.
     *
     * Le système de couleurs fonctionne ainsi :
     * - Vert : Niveau de batterie > 70% (excellent état)
     * - Orange : Niveau de batterie entre 30% et 70% (état moyen)
     * - Rouge clair : Niveau de batterie entre 20% et 30% (état faible)
     * - Rouge moyen : Niveau de batterie entre 10% et 20% (état très faible)
     * - Rouge foncé : Niveau de batterie ≤ 10% (état critique)
     *
     * Le processus de mise à jour fonctionne ainsi :
     * Étape 1 : Analyser le niveau de batterie actuel
     * Étape 2 : Déterminer la couleur et le statut appropriés
     * Étape 3 : Enregistrer l'état de batterie dans les logs
     * Étape 4 : Appliquer la nouvelle couleur au robot
     */
    private void updateRobotColor() {
        // Variables pour stocker la nouvelle couleur et le statut
        int[] nouvelleCouleur;
        String statutBatterie;

        // Étape 1 et 2 : Analyser le niveau et déterminer la couleur appropriée
        if (batteryLevel > 70) {
            // Excellent niveau de batterie - vert vif
            nouvelleCouleur = new int[]{0, 255, 0};
            statutBatterie = "EXCELLENT";
        } else if (batteryLevel > 30) {
            // Niveau de batterie moyen - orange
            nouvelleCouleur = new int[]{255, 165, 0};
            statutBatterie = "MOYEN";
        } else if (batteryLevel > 20) {
            // Niveau de batterie faible - rouge clair
            nouvelleCouleur = new int[]{255, 100, 100};
            statutBatterie = "FAIBLE";
        } else if (batteryLevel > 10) {
            // Niveau de batterie très faible - rouge moyen
            nouvelleCouleur = new int[]{255, 50, 50};
            statutBatterie = "TRES FAIBLE";
        } else {
            // Niveau de batterie critique - rouge foncé
            nouvelleCouleur = new int[]{255, 0, 0};
            statutBatterie = "CRITIQUE";
        }

        // Étape 3 : Enregistrer l'état de batterie dans les logs pour le suivi
        LogManager.getInstance().logBattery(getName(), batteryLevel, "État: " + statutBatterie);

        // Étape 4 : Appliquer la nouvelle couleur au robot pour l'affichage visuel
        this.setColor(nouvelleCouleur);
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LA BATTERIE EST À UN NIVEAU CRITIQUE
     *
     * Cette méthode est essentielle pour la gestion autonome de l'énergie du robot.
     * Elle détermine si le niveau de batterie actuel nécessite une action immédiate
     * pour éviter un arrêt complet du robot pendant une mission.
     *
     * Le processus de vérification fonctionne ainsi :
     * 1. Comparer le niveau de batterie actuel avec le seuil critique
     * 2. Le seuil critique est défini par CRITICAL_BATTERY_THRESHOLD (5.0%)
     * 3. Retourner true si le niveau est égal ou inférieur au seuil
     * 4. Cette information déclenche des actions d'urgence
     *
     * Quand la batterie est critique, le robot doit :
     * - Abandonner sa mission actuelle si nécessaire
     * - Se diriger immédiatement vers la station de recharge la plus proche
     * - Informer les autres robots de son état d'urgence
     * - Éviter de prendre de nouvelles tâches
     *
     * Le seuil de 5% est volontairement bas pour maximiser l'efficacité,
     * mais assez élevé pour permettre d'atteindre une station de recharge.
     *
     * Cette méthode est utilisée dans :
     * - La boucle principale step() pour les décisions d'urgence
     * - Les algorithmes de planification de trajet
     * - Les systèmes d'alerte et de communication
     * - La gestion des priorités de tâches
     *
     * @return true si la batterie est à un niveau critique, false sinon
     */
    private boolean isBatteryCritical() {
        // Étape 1 : Comparer le niveau actuel avec le seuil critique
        boolean niveauCritique = batteryLevel <= CRITICAL_BATTERY_THRESHOLD;

        // Étape 2 : Si critique, enregistrer l'alerte dans les logs
        if (niveauCritique) {
            LogManager.getInstance().logBattery(getName(), batteryLevel,
                "ALERTE : Niveau de batterie CRITIQUE (≤ " + CRITICAL_BATTERY_THRESHOLD + "%)");
        }

        // Étape 3 : Retourner le résultat de la vérification
        return niveauCritique;
    }

    /**
     * Prédire si le robot a assez de batterie pour atteindre une destination
     * Version extrêmement optimisée avec usage ultra-agressif de la batterie
     * @param destX Coordonnée X de la destination
     * @param destY Coordonnée Y de la destination
     * @return true si le robot a assez de batterie pour atteindre la destination, false sinon
     */
    public boolean canReachDestination(int destX, int destY) {
        // Calculer la distance de Manhattan vers la destination
        int distance = Math.abs(this.getX() - destX) + Math.abs(this.getY() - destY);

        // Calculer la batterie nécessaire pour le voyage (mouvement + dépôt)
        double batteryNeeded = (distance * MOVE_BATTERY_COST) + DEPOSIT_BATTERY_COST;

        // OPTIMISATION EXTRÊME : Aucune marge de sécurité du tout
        // C'est risqué mais maximise l'efficacité

        // OPTIMISATION EXTRÊME : Pour les courtes distances, être extrêmement agressif
        if (distance <= 10) {
            // Pour les courtes distances, nous n'avons besoin que de 80% de la batterie calculée
            // C'est parce que nous pouvons souvent trouver des chemins plus efficaces pendant le mouvement
            return batteryLevel >= batteryNeeded * 0.8;
        }

        // OPTIMISATION EXTRÊME : Pour les distances moyennes, être très agressif
        if (distance <= 20) {
            // Pour les distances moyennes, nous avons besoin de 90% de la batterie calculée
            return batteryLevel >= batteryNeeded * 0.9;
        }

        // Pour les longues distances, utiliser le montant exact calculé
        return batteryLevel >= batteryNeeded;
    }

    /**
     * MÉTHODE POUR DIFFUSER UN MESSAGE SIMPLE À TOUS LES ROBOTS
     *
     * Cette méthode fait partie du système de communication legacy qui utilise
     * des messages textuels simples. Elle envoie un message à tous les autres
     * robots de type MyTransitRobot présents dans l'environnement.
     *
     * Le processus de diffusion fonctionne ainsi :
     * Étape 1 : Créer un message simple avec le contenu fourni
     * Étape 2 : Récupérer la liste de tous les robots dans l'environnement
     * Étape 3 : Parcourir chaque robot de la liste
     * Étape 4 : Vérifier que c'est un autre MyTransitRobot (pas nous-même)
     * Étape 5 : Envoyer le message à ce robot
     *
     * @param content Le contenu textuel du message à diffuser
     */
    private void broadcastMessage(String content) {
        // Étape 1 : Créer un message simple avec le contenu fourni
        SimpleMessage messageADiffuser = new SimpleMessage(this, content);

        // Étape 2 : Récupérer la liste de tous les robots dans l'environnement
        List<Robot> tousLesRobots = environnement.getRobot();

        // Étape 3 : Parcourir chaque robot de la liste
        for (Robot robot : tousLesRobots) {
            // Étape 4 : Vérifier que c'est un autre MyTransitRobot (pas nous-même)
            if (robot != this && robot instanceof MyTransitRobot) {
                // Étape 5 : Envoyer le message à ce robot
                ((MyTransitRobot)robot).handleSimpleMessage(messageADiffuser);
            }
        }
    }

    /**
     * MÉTHODE POUR DIFFUSER UN MESSAGE D'ALERTE BATTERIE FAIBLE
     *
     * Cette méthode envoie une alerte à tous les autres robots pour les informer
     * que ce robot a une batterie faible. Cela permet aux autres robots de :
     * - Éviter de compter sur ce robot pour des tâches
     * - Offrir leur aide si possible
     * - Adapter leurs stratégies de coordination
     *
     * Le processus fonctionne ainsi :
     * Étape 1 : Construire le message avec les informations de position et batterie
     * Étape 2 : Diffuser le message à tous les robots via broadcastMessage
     */
    private void broadcastLowBatteryMessage() {
        // Étape 1 : Construire le message avec position et niveau de batterie
        String contenuMessage = MSG_LOW_BATTERY + "|" + this.getX() + "|" + this.getY() + "|" + (int)batteryLevel;

        // Étape 2 : Diffuser le message à tous les robots
        broadcastMessage(contenuMessage);
    }

    /**
     * MÉTHODE POUR DIFFUSER UNE DEMANDE D'AIDE AUX AUTRES ROBOTS
     *
     * Cette méthode envoie une demande d'aide quand le robot se trouve dans
     * une situation difficile (batterie critique, blocage, etc.). Elle évite
     * d'envoyer plusieurs demandes consécutives grâce au flag waitingForHelp.
     *
     * Le processus fonctionne ainsi :
     * Étape 1 : Vérifier qu'on n'attend pas déjà une réponse
     * Étape 2 : Construire le message de base avec la position
     * Étape 3 : Ajouter des informations sur le colis si applicable
     * Étape 4 : Diffuser la demande d'aide
     * Étape 5 : Marquer qu'on attend une réponse
     */
    private void broadcastHelpRequest() {
        // Étape 1 : Vérifier qu'on n'attend pas déjà une réponse d'aide
        if (!waitingForHelp) {
            // Étape 2 : Construire le message de base avec notre position
            String contenuMessage = MSG_HELP_REQUEST + "|" + this.getX() + "|" + this.getY();

            // Étape 3 : Ajouter des informations sur le colis si on en transporte un
            if (colisTransporte != null) {
                contenuMessage += "|" + colisTransporte.getDestinationGoalId();
            }

            // Étape 4 : Diffuser la demande d'aide à tous les robots
            broadcastMessage(contenuMessage);

            // Étape 5 : Marquer qu'on attend maintenant une réponse
            waitingForHelp = true;
        }
    }

    /**
     * MÉTHODE POUR GÉRER LES MESSAGES ENTRANTS DU FRAMEWORK DE SIMULATION
     *
     * Cette méthode est appelée automatiquement par le framework de simulation
     * quand le robot reçoit un message du système legacy. Elle fait le pont
     * entre l'ancien système de messages du framework et notre système de
     * communication moderne.
     *
     * Le processus de gestion fonctionne ainsi :
     * Étape 1 : Recevoir le message du framework de simulation
     * Étape 2 : Extraire le contenu textuel du message
     * Étape 3 : Créer un SimpleMessage compatible avec notre système
     * Étape 4 : Déléguer le traitement à notre gestionnaire de messages simples
     *
     * @param msg Le message reçu du framework de simulation
     */
    @Override
    public void handleMessage(fr.emse.fayol.maqit.simulator.components.Message msg) {
        // Étape 1 : Le message est reçu automatiquement du framework

        // Étape 2 : Extraire le contenu textuel du message
        String contenuMessage = msg.getContent();

        // Étape 3 : Créer un SimpleMessage compatible avec notre système
        SimpleMessage messageSimple = new SimpleMessage(this, contenuMessage);

        // Étape 4 : Déléguer le traitement à notre gestionnaire de messages simples
        handleSimpleMessage(messageSimple);
    }

    /**
     * MÉTHODE POUR TRAITER LES MESSAGES DU SYSTÈME D'ENCHÈRES DÉCENTRALISÉ
     *
     * Cette méthode est le point d'entrée principal pour tous les messages
     * provenant du système de communication décentralisé entre robots.
     * Elle fait partie de l'architecture d'enchères qui remplace le
     * coordinateur centralisé par un système distribué plus robuste.
     *
     * Le processus de traitement fonctionne ainsi :
     * 1. Recevoir un message de type Message (interface commune)
     * 2. Déléguer le traitement au message lui-même (pattern Visitor)
     * 3. Le message appelle les méthodes appropriées sur ce robot
     * 4. Chaque type de message implémente sa propre logique de traitement
     *
     * Types de messages supportés dans le système d'enchères :
     * - TaskAnnouncementMessage : Annonce de nouvelles tâches disponibles
     * - BidMessage : Offres d'enchères pour des tâches spécifiques
     * - TaskAssignmentMessage : Attribution de tâches aux robots gagnants
     * - TaskCompletedMessage : Notification de completion de tâches
     * - TransitZoneStatusMessage : Mise à jour du statut des zones de transit
     * - EfficiencyUpdateMessage : Partage des statistiques d'efficacité
     *
     * Cette architecture décentralisée offre plusieurs avantages :
     * - Résistance aux pannes (pas de point unique de défaillance)
     * - Scalabilité (ajout facile de nouveaux robots)
     * - Flexibilité (adaptation dynamique aux conditions)
     * - Performance (décisions locales rapides)
     *
     * @param msg Le message à traiter (implémente l'interface Message)
     */
    public void handleMessage(Message msg) {
        // Étape 1 : Enregistrer la réception du message pour le débogage
        LogManager.getInstance().logCoordination(getName(),
            "Réception d'un message de type " + msg.getMessageType() +
            " de " + msg.getSender().getName());

        // Étape 2 : Déléguer le traitement au message (pattern Visitor)
        // Chaque type de message sait comment se traiter lui-même
        // Cela permet d'ajouter facilement de nouveaux types de messages
        // sans modifier cette méthode
        msg.process(this);

        // Étape 3 : Enregistrer la completion du traitement
        LogManager.getInstance().logCoordination(getName(),
            "Traitement terminé pour le message de type " + msg.getMessageType());
    }

    /**
     * MÉTHODE POUR TRAITER LES MESSAGES SIMPLES (SYSTÈME LEGACY)
     *
     * Cette méthode gère les messages du système de communication legacy
     * qui utilise des chaînes de caractères avec des séparateurs pour
     * transmettre des informations entre robots. Bien que remplacé par
     * le système d'enchères moderne, ce système reste actif pour la
     * compatibilité avec les anciens composants.
     *
     * Le processus de traitement fonctionne ainsi :
     * 1. Extraire le contenu textuel du message
     * 2. Diviser le contenu en parties séparées par des "|"
     * 3. Identifier le type de message (première partie)
     * 4. Traiter chaque type de message selon sa logique spécifique
     * 5. Mettre à jour l'état du robot ou du coordinateur si nécessaire
     *
     * Types de messages legacy supportés :
     * - MSG_LOW_BATTERY : Alerte de batterie faible d'un autre robot
     * - MSG_HELP_REQUEST : Demande d'aide d'un robot en difficulté
     * - MSG_HELP_OFFER : Offre d'assistance d'un robot disponible
     * - MSG_TRANSIT_FULL : Notification qu'une zone de transit est pleine
     * - MSG_TRANSIT_AVAILABLE : Notification qu'une zone de transit est disponible
     *
     * Ce système permet la coordination basique entre robots pour :
     * - La gestion des urgences (batterie faible)
     * - L'entraide mutuelle entre robots
     * - Le partage d'informations sur les zones de transit
     * - La synchronisation des états du système
     *
     * @param msg Le message simple à traiter (format texte legacy)
     */
    public void handleSimpleMessage(SimpleMessage msg) {
        // Étape 1 : Extraire le contenu textuel du message
        String contenuMessage = msg.getContent();

        // Étape 2 : Diviser le contenu en parties séparées par "|"
        String[] partiesMessage = contenuMessage.split("\\|");

        // Vérifier qu'il y a au moins une partie (le type de message)
        if (partiesMessage.length > 0) {
            // Étape 3 : Identifier le type de message
            String typeMessage = partiesMessage[0];

            // Étape 4 : Traiter chaque type de message selon sa logique
            if (typeMessage.equals(MSG_LOW_BATTERY)) {
                // Un autre robot a une batterie faible
                LogManager.getInstance().logCoordination(getName(),
                    "Alerte batterie faible reçue de " + msg.getSender().getName());

            } else if (typeMessage.equals(MSG_HELP_REQUEST)) {
                // Un autre robot demande de l'aide
                if (etatActuel == EtatRobot.LIBRE && batteryLevel > CRITICAL_BATTERY_THRESHOLD) {
                    // Nous sommes libres et avons assez de batterie pour aider
                    LogManager.getInstance().logCoordination(getName(),
                        "Offre d'aide envoyée à " + msg.getSender().getName());

                    // Préparer une réponse d'offre d'aide
                    String reponse = MSG_HELP_OFFER + "|" + this.getX() + "|" + this.getY();
                    SimpleMessage messageReponse = new SimpleMessage(this, reponse);
                    msg.getSender().handleSimpleMessage(messageReponse);
                }

            } else if (typeMessage.equals(MSG_HELP_OFFER)) {
                // Un autre robot offre son aide
                if (waitingForHelp) {
                    LogManager.getInstance().logCoordination(getName(),
                        "Offre d'aide acceptée de " + msg.getSender().getName());
                    waitingForHelp = false;
                }

            } else if (typeMessage.equals(MSG_TRANSIT_FULL)) {
                // Une zone de transit est pleine
                if (partiesMessage.length >= 3) {
                    int coordXTransit = Integer.parseInt(partiesMessage[1]);
                    int coordYTransit = Integer.parseInt(partiesMessage[2]);

                    LogManager.getInstance().logTransit(getName(),
                        "Zone (" + coordXTransit + "," + coordYTransit + ")",
                        "informé que la zone de transit est pleine");

                    // Étape 5 : Mettre à jour le coordinateur de tâches
                    taskCoordinator.handleTransitZoneStatus(coordXTransit, coordYTransit, true);
                }

            } else if (typeMessage.equals(MSG_TRANSIT_AVAILABLE)) {
                // Une zone de transit a de l'espace disponible
                if (partiesMessage.length >= 3) {
                    int coordXTransit = Integer.parseInt(partiesMessage[1]);
                    int coordYTransit = Integer.parseInt(partiesMessage[2]);

                    LogManager.getInstance().logTransit(getName(),
                        "Zone (" + coordXTransit + "," + coordYTransit + ")",
                        "informé que la zone de transit est disponible");

                    // Étape 5 : Mettre à jour le coordinateur de tâches
                    taskCoordinator.handleTransitZoneStatus(coordXTransit, coordYTransit, false);
                }
            } else {
                // Type de message non reconnu
                LogManager.getInstance().logCoordination(getName(),
                    "Message de type inconnu reçu : " + typeMessage);
            }
        }
    }
}