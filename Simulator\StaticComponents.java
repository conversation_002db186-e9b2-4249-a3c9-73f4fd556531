package Simulator;

import fr.emse.fayol.maqit.simulator.components.SituatedComponent;
import fr.emse.fayol.maqit.simulator.components.ColorTransitZone;
import fr.emse.fayol.maqit.simulator.components.ColorStartZone;
import fr.emse.fayol.maqit.simulator.components.ColorExitZone;
import fr.emse.fayol.maqit.simulator.components.ColorObstacle;
import fr.emse.fayol.maqit.simulator.environment.ColorCell;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * FICHIER DES COMPOSANTS STATIQUES
 *
 * Ce fichier regroupe toutes les classes liées aux composants statiques qui doivent
 * rester immobiles pendant la simulation. Ces composants incluent :
 * - Les zones de transit (où les robots déposent temporairement les colis)
 * - Les zones de départ (où apparaissent les colis à livrer)
 * - Les zones de sortie (destinations finales)
 * - Les obstacles (murs, barrières, etc.)
 *
 * Pourquoi ces composants doivent-ils être statiques ?
 * - Ils représentent l'infrastructure fixe de l'entrepôt
 * - Leur déplacement casserait la logique de la simulation
 * - Les robots comptent sur leur position fixe pour naviguer
 *
 * Le système empêche activement tout déplacement de ces composants.
 */

/**
 * CLASSE ABSTRAITE POUR LES ZONES STATIQUES
 *
 * Cette classe abstraite sert de base pour toutes les zones qui doivent rester
 * immobiles dans la simulation. Elle hérite de SituatedComponent (qui permet
 * normalement le déplacement) mais surcharge les méthodes de déplacement
 * pour les rendre inopérantes.
 *
 * Principe de fonctionnement :
 * - Hérite de SituatedComponent pour être compatible avec le système
 * - Surcharge setLocation() pour empêcher tout changement de position
 * - Enregistre les tentatives de déplacement comme des erreurs
 *
 * Cette approche garantit que même si du code essaie de déplacer ces zones,
 * elles resteront à leur position initiale.
 */
abstract class ZoneStatique extends SituatedComponent {

    /**
     * CONSTRUCTEUR DE LA ZONE STATIQUE
     *
     * Ce constructeur initialise une zone statique à une position donnée.
     * Une fois créée, cette zone ne pourra plus jamais changer de position.
     *
     * @param position Position initiale et définitive de la zone [x, y]
     */
    public ZoneStatique(int[] position) {
        // Appeler le constructeur de la classe parente avec la position
        super(position);

        // Enregistrer la création de la zone statique
        LogManager.getInstance().logAction("ZoneStatique",
            "Zone statique créée à la position [" + position[0] + "," + position[1] + "]");
    }

    /**
     * MÉTHODE SURCHARGÉE POUR EMPÊCHER LE DÉPLACEMENT
     *
     * Cette méthode surcharge setLocation() de la classe parente pour empêcher
     * tout changement de position. Quand du code essaie de déplacer cette zone,
     * la méthode ne fait rien et enregistre une erreur dans les logs.
     *
     * Pourquoi cette approche ?
     * - Plus sûr que de lever une exception (ne casse pas la simulation)
     * - Permet de détecter les tentatives de déplacement non autorisées
     * - Maintient la compatibilité avec le système existant
     *
     * @param nouvellePosition Position demandée (ignorée)
     */
    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones statiques ne doivent jamais changer de position
        // Enregistrer cette tentative comme une erreur pour le débogage
        LogManager.getInstance().logError("ZoneStatique",
            "Tentative de déplacement d'une zone statique empêchée - " +
            "Position actuelle: [" + getLocation()[0] + "," + getLocation()[1] + "], " +
            "Position demandée: [" + nouvellePosition[0] + "," + nouvellePosition[1] + "]");
    }
}

/**
 * CLASSE POUR LES ZONES DE TRANSIT STATIQUES
 *
 * Cette classe étend ColorTransitZone pour créer des zones de transit qui ne peuvent
 * pas bouger. Les zones de transit sont des endroits où les robots déposent
 * temporairement leurs colis avant qu'ils soient récupérés par d'autres robots.
 *
 * Caractéristiques des zones de transit :
 * - Ont une capacité limitée (nombre maximum de colis qu'elles peuvent contenir)
 * - Servent d'intermédiaire entre les zones de départ et d'arrivée
 * - Doivent rester à leur position fixe pour que les robots puissent les trouver
 *
 * Cette classe empêche tout déplacement de ces zones critiques.
 */
class StaticTransitZone extends ColorTransitZone {

    /**
     * CONSTRUCTEUR DE LA ZONE DE TRANSIT STATIQUE
     *
     * Ce constructeur crée une zone de transit avec une position fixe, une couleur
     * d'affichage et une capacité maximale de stockage.
     *
     * @param position Position fixe de la zone [x, y]
     * @param couleur Couleur d'affichage de la zone [rouge, vert, bleu]
     * @param capacite Nombre maximum de colis que peut contenir la zone
     */
    public StaticTransitZone(int[] position, int[] couleur, int capacite) {
        // Appeler le constructeur de la classe parente
        super(position, couleur, capacite);

        // Enregistrer la création de cette zone de transit
        LogManager.getInstance().logAction("StaticTransitZone",
            "Zone de transit statique créée à [" + position[0] + "," + position[1] + "] avec capacité " + capacite);
    }

    /**
     * MÉTHODE SURCHARGÉE POUR EMPÊCHER LE DÉPLACEMENT
     *
     * Cette méthode empêche tout déplacement de la zone de transit.
     * Les zones de transit doivent rester fixes car les robots comptent
     * sur leur position pour déposer et récupérer les colis.
     *
     * @param nouvellePosition Position demandée (ignorée)
     */
    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones de transit doivent rester immobiles
        LogManager.getInstance().logError("StaticTransitZone",
            "Tentative de déplacement d'une zone de transit empêchée - " +
            "Position actuelle: [" + getLocation()[0] + "," + getLocation()[1] + "]");
    }
}

/**
 * CLASSE POUR LES ZONES DE DÉPART STATIQUES
 *
 * Cette classe étend ColorStartZone pour créer des zones de départ qui ne peuvent
 * pas bouger. Les zones de départ sont les endroits où apparaissent les nouveaux
 * colis à livrer dans la simulation.
 *
 * Caractéristiques des zones de départ :
 * - C'est là que les colis sont générés au début de leur cycle de vie
 * - Les robots viennent y chercher les colis à transporter
 * - Leur position doit être connue et fixe pour la logique de génération
 *
 * Cette classe empêche tout déplacement de ces zones sources.
 */
class StaticStartZone extends ColorStartZone {

    /**
     * CONSTRUCTEUR DE LA ZONE DE DÉPART STATIQUE
     *
     * Ce constructeur crée une zone de départ avec une position fixe et une couleur
     * d'affichage. Cette zone servira de point d'origine pour les colis.
     *
     * @param position Position fixe de la zone [x, y]
     * @param couleur Couleur d'affichage de la zone [rouge, vert, bleu]
     */
    public StaticStartZone(int[] position, int[] couleur) {
        // Appeler le constructeur de la classe parente
        super(position, couleur);

        // Enregistrer la création de cette zone de départ
        LogManager.getInstance().logAction("StaticStartZone",
            "Zone de départ statique créée à [" + position[0] + "," + position[1] + "]");
    }

    /**
     * MÉTHODE SURCHARGÉE POUR EMPÊCHER LE DÉPLACEMENT
     *
     * Cette méthode empêche tout déplacement de la zone de départ.
     * Les zones de départ doivent rester fixes car c'est là que les colis
     * sont générés et où les robots viennent les chercher.
     *
     * @param nouvellePosition Position demandée (ignorée)
     */
    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones de départ doivent rester immobiles
        LogManager.getInstance().logError("StaticStartZone",
            "Tentative de déplacement d'une zone de départ empêchée - " +
            "Position actuelle: [" + getLocation()[0] + "," + getLocation()[1] + "]");
    }
}

/**
 * CLASSE POUR LES ZONES DE SORTIE STATIQUES
 *
 * Cette classe étend ColorExitZone pour créer des zones de sortie qui ne peuvent
 * pas bouger. Les zones de sortie sont les destinations finales où les robots
 * déposent les colis une fois leur livraison terminée.
 *
 * Caractéristiques des zones de sortie :
 * - Destination finale des colis dans leur cycle de vie
 * - Point où les colis "sortent" de la simulation
 * - Leur position doit être connue pour que les robots puissent les atteindre
 *
 * Cette classe empêche tout déplacement de ces zones destinations.
 */
class StaticExitZone extends ColorExitZone {

    /**
     * CONSTRUCTEUR DE LA ZONE DE SORTIE STATIQUE
     *
     * Ce constructeur crée une zone de sortie avec une position fixe et une couleur
     * d'affichage. Cette zone servira de destination finale pour les colis.
     *
     * @param position Position fixe de la zone [x, y]
     * @param couleur Couleur d'affichage de la zone [rouge, vert, bleu]
     */
    public StaticExitZone(int[] position, int[] couleur) {
        // Appeler le constructeur de la classe parente
        super(position, couleur);

        // Enregistrer la création de cette zone de sortie
        LogManager.getInstance().logAction("StaticExitZone",
            "Zone de sortie statique créée à [" + position[0] + "," + position[1] + "]");
    }

    /**
     * MÉTHODE SURCHARGÉE POUR EMPÊCHER LE DÉPLACEMENT
     *
     * Cette méthode empêche tout déplacement de la zone de sortie.
     * Les zones de sortie doivent rester fixes car c'est la destination
     * finale que les robots doivent atteindre pour terminer leurs livraisons.
     *
     * @param nouvellePosition Position demandée (ignorée)
     */
    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones de sortie doivent rester immobiles
        LogManager.getInstance().logError("StaticExitZone",
            "Tentative de déplacement d'une zone de sortie empêchée - " +
            "Position actuelle: [" + getLocation()[0] + "," + getLocation()[1] + "]");
    }
}

/**
 * CLASSE POUR LES OBSTACLES STATIQUES
 *
 * Cette classe étend ColorObstacle pour créer des obstacles qui ne peuvent
 * pas bouger. Les obstacles représentent les murs, barrières et autres
 * éléments physiques qui bloquent le passage des robots.
 *
 * Caractéristiques des obstacles :
 * - Bloquent le passage des robots (cases infranchissables)
 * - Représentent l'infrastructure physique de l'entrepôt
 * - Leur position doit être fixe pour que les robots puissent planifier leurs trajets
 *
 * Cette classe empêche tout déplacement de ces éléments structurels.
 */
class StaticObstacle extends ColorObstacle {

    /**
     * CONSTRUCTEUR DE L'OBSTACLE STATIQUE
     *
     * Ce constructeur crée un obstacle avec une position fixe et une couleur
     * d'affichage. Cet obstacle bloquera définitivement le passage à cette position.
     *
     * @param position Position fixe de l'obstacle [x, y]
     * @param couleur Couleur d'affichage de l'obstacle [rouge, vert, bleu]
     */
    public StaticObstacle(int[] position, int[] couleur) {
        // Appeler le constructeur de la classe parente
        super(position, couleur);

        // Enregistrer la création de cet obstacle
        LogManager.getInstance().logAction("StaticObstacle",
            "Obstacle statique créé à [" + position[0] + "," + position[1] + "]");
    }

    /**
     * MÉTHODE SURCHARGÉE POUR EMPÊCHER LE DÉPLACEMENT
     *
     * Cette méthode empêche tout déplacement de l'obstacle.
     * Les obstacles doivent rester fixes car ils représentent l'infrastructure
     * physique de l'entrepôt (murs, colonnes, etc.).
     *
     * @param nouvellePosition Position demandée (ignorée)
     */
    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les obstacles doivent rester immobiles
        LogManager.getInstance().logError("StaticObstacle",
            "Tentative de déplacement d'un obstacle empêchée - " +
            "Position actuelle: [" + getLocation()[0] + "," + getLocation()[1] + "]");
    }
}

/**
 * GESTIONNAIRE DES COMPOSANTS STATIQUES
 *
 * Cette classe gère tous les composants statiques de la simulation et s'assure
 * qu'ils ne sont jamais déplacés pendant l'exécution. Elle utilise le pattern
 * Singleton pour garantir qu'il n'y a qu'un seul gestionnaire dans toute la simulation.
 *
 * Responsabilités du gestionnaire :
 * - Enregistrer tous les composants statiques lors de leur création
 * - Vérifier si un composant est statique avant d'autoriser son déplacement
 * - Détecter et corriger les tentatives de déplacement non autorisées
 * - Maintenir une liste de tous les composants qui ne doivent pas bouger
 *
 * Ce système de protection empêche les bugs qui pourraient casser la simulation
 * en déplaçant accidentellement des éléments fixes comme les murs ou les zones.
 */
class StaticComponentManager {

    // Instance unique du gestionnaire (pattern Singleton)
    private static StaticComponentManager instanceUnique;

    // Liste de tous les composants statiques enregistrés
    private List<SituatedComponent> composantsStatiques = new ArrayList<>();

    // Référence vers l'environnement de simulation
    private ColorGridEnvironment environnement;

    /**
     * CONSTRUCTEUR PRIVÉ (PATTERN SINGLETON)
     *
     * Ce constructeur est privé pour empêcher la création directe d'instances.
     * Il faut passer par getInstance() pour obtenir l'instance unique.
     */
    private StaticComponentManager() {
        // Enregistrer la création du gestionnaire
        LogManager.getInstance().logAction("StaticComponentManager",
            "Gestionnaire de composants statiques créé");
    }

    /**
     * MÉTHODE POUR OBTENIR L'INSTANCE UNIQUE (PATTERN SINGLETON)
     *
     * Cette méthode est le seul moyen d'obtenir une instance du gestionnaire.
     * Elle crée l'instance si elle n'existe pas encore, sinon elle retourne
     * l'instance existante.
     *
     * @return L'instance unique du gestionnaire
     */
    public static StaticComponentManager getInstance() {
        // Vérifier si l'instance n'existe pas encore
        if (instanceUnique == null) {
            // Créer la première et unique instance
            instanceUnique = new StaticComponentManager();
        }
        // Retourner l'instance (nouvelle ou existante)
        return instanceUnique;
    }

    /**
     * MÉTHODE POUR DÉFINIR L'ENVIRONNEMENT DE SIMULATION
     *
     * Cette méthode permet d'associer l'environnement de simulation au gestionnaire.
     * L'environnement est nécessaire pour vérifier les positions des composants
     * et effectuer des opérations de déplacement si nécessaire.
     *
     * @param env L'environnement de simulation (la grille)
     */
    public void setEnvironment(ColorGridEnvironment env) {
        this.environnement = env;

        // Enregistrer l'association dans les logs
        LogManager.getInstance().logAction("StaticComponentManager",
            "Environnement de simulation associé au gestionnaire");
    }

    /**
     * MÉTHODE POUR ENREGISTRER UN COMPOSANT STATIQUE
     *
     * Cette méthode ajoute un composant à la liste des composants statiques.
     * Une fois enregistré, ce composant sera protégé contre tout déplacement.
     *
     * @param composant Le composant à protéger contre le déplacement
     */
    public void registerStaticComponent(SituatedComponent composant) {
        // Ajouter le composant à la liste des composants protégés
        composantsStatiques.add(composant);

        // Enregistrer cette action dans les logs
        LogManager.getInstance().logAction("StaticComponentManager",
            "Composant statique enregistré à la position [" +
            composant.getLocation()[0] + "," + composant.getLocation()[1] + "]");
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI UN COMPOSANT EST STATIQUE
     *
     * Cette méthode détermine si un composant donné doit être considéré comme statique.
     * Un composant est statique s'il est soit :
     * - Explicitement enregistré dans la liste des composants statiques
     * - Une instance d'une des classes statiques (StaticTransitZone, etc.)
     *
     * @param composant Le composant à vérifier
     * @return true si le composant est statique, false sinon
     */
    public boolean isStaticComponent(SituatedComponent composant) {
        // Vérifier que le composant n'est pas null
        if (composant == null) return false;

        // Vérifier si le composant est dans la liste OU est une instance d'une classe statique
        return composantsStatiques.contains(composant) ||
               composant instanceof StaticTransitZone ||
               composant instanceof StaticStartZone ||
               composant instanceof StaticExitZone ||
               composant instanceof StaticObstacle;
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI UNE POSITION CONTIENT UN COMPOSANT STATIQUE
     *
     * Cette méthode examine une position donnée dans la grille pour déterminer
     * si elle contient un composant statique. C'est utile pour vérifier les
     * positions avant d'effectuer des opérations.
     *
     * @param x Coordonnée X de la position à vérifier
     * @param y Coordonnée Y de la position à vérifier
     * @return true si la position contient un composant statique, false sinon
     */
    public boolean isStaticComponentAt(int x, int y) {
        // Vérifier que l'environnement est disponible
        if (environnement == null) return false;

        // Récupérer la cellule à cette position
        ColorCell cellule = (ColorCell) environnement.getGrid()[x][y];
        if (cellule == null || cellule.getContent() == null) return false;

        // Vérifier si le contenu de la cellule est un composant statique
        return isStaticComponent(cellule.getContent());
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI UN COMPOSANT PEUT ÊTRE DÉPLACÉ
     *
     * Cette méthode détermine si un composant donné peut être déplacé.
     * Un composant peut être déplacé seulement s'il n'est pas statique.
     *
     * @param composant Le composant dont on veut vérifier la mobilité
     * @return true si le déplacement est autorisé, false sinon
     */
    public boolean canMove(SituatedComponent composant) {
        // Un composant peut bouger seulement s'il n'est pas statique
        return !isStaticComponent(composant);
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI UN DÉPLACEMENT ENTRE POSITIONS EST AUTORISÉ
     *
     * Cette méthode vérifie si un déplacement d'une position à une autre est autorisé
     * en examinant le composant qui se trouve à la position de départ.
     *
     * @param positionDepart Position de départ [x, y]
     * @param positionArrivee Position d'arrivée [x, y]
     * @return true si le déplacement est autorisé, false sinon
     */
    public boolean canMove(int[] positionDepart, int[] positionArrivee) {
        // Si l'environnement n'est pas disponible, autoriser par défaut
        if (environnement == null) return true;

        // Récupérer la cellule de départ
        ColorCell celluleDepart = (ColorCell) environnement.getGrid()[positionDepart[0]][positionDepart[1]];
        if (celluleDepart == null || celluleDepart.getContent() == null) return true;

        // Vérifier si le composant à la position de départ peut être déplacé
        return !isStaticComponent(celluleDepart.getContent());
    }

    /**
     * MÉTHODE POUR RESTAURER LA POSITION D'UN COMPOSANT STATIQUE
     *
     * Cette méthode vérifie si un composant statique a été déplacé de sa position
     * originale et le remet à sa place si nécessaire. C'est une mesure de sécurité
     * pour corriger les déplacements non autorisés.
     *
     * @param composant Le composant à vérifier
     * @param positionOriginale La position originale où le composant devrait être [x, y]
     */
    public void ensureStaticComponentPosition(SituatedComponent composant, int[] positionOriginale) {
        // Vérifier seulement les composants statiques
        if (isStaticComponent(composant)) {
            // Récupérer la position actuelle du composant
            int[] positionActuelle = composant.getLocation();

            // Vérifier si le composant a été déplacé
            if (positionActuelle[0] != positionOriginale[0] || positionActuelle[1] != positionOriginale[1]) {
                // Le composant a été déplacé, enregistrer l'erreur
                LogManager.getInstance().logError("StaticComponentManager",
                    "Composant statique déplacé détecté! Restauration de la position originale " +
                    Arrays.toString(positionOriginale));

                // Restaurer la position correcte
                environnement.moveComponent(positionActuelle, positionOriginale);
            }
        }
    }
}
