package Simulator;

import fr.emse.fayol.maqit.simulator.components.ColorInteractionRobot;
import fr.emse.fayol.maqit.simulator.components.ColorPackage;
import fr.emse.fayol.maqit.simulator.components.ColorStartZone;
import fr.emse.fayol.maqit.simulator.components.Message;
import fr.emse.fayol.maqit.simulator.components.Orientation;
import fr.emse.fayol.maqit.simulator.components.PackageState;
import fr.emse.fayol.maqit.simulator.environment.Cell;
import fr.emse.fayol.maqit.simulator.environment.ColorCell;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;
import fr.emse.fayol.maqit.simulator.environment.Location;

import java.awt.Color;
import java.util.HashMap;
import java.util.Map;

/**
 * CLASSE MYROBOT - ROBOT DE BASE POUR LA LIVRAISON DE COLIS
 *
 * Cette classe représente un robot de base qui peut transporter des colis dans l'entrepôt.
 * C'est la classe de base pour tous les robots de livraison dans la simulation.
 *
 * Fonctionnement du robot :
 * 1. État LIBRE : Le robot cherche une zone de départ avec des colis
 * 2. État TRANSPORT : Le robot transporte un colis vers sa destination
 * 3. État LIVRÉ : Le robot a terminé sa livraison et disparaît
 *
 * Le robot utilise un algorithme simple :
 * - Il se déplace vers la zone de départ la plus proche qui a des colis
 * - Il prend un colis et se dirige vers la destination correspondante
 * - Il livre le colis et disparaît de la simulation
 *
 * Cette classe peut être étendue (comme MyTransitRobot) pour ajouter des fonctionnalités
 * comme l'utilisation de zones de transit ou la gestion de batterie.
 */
public class MyRobot extends ColorInteractionRobot {

    /**
     * ÉNUMÉRATION DES ÉTATS DU ROBOT
     *
     * Cette énumération définit les trois états possibles d'un robot :
     * - FREE (LIBRE) : Le robot n'a pas de colis et cherche une tâche
     * - TRANSPORT : Le robot transporte un colis vers sa destination
     * - DELIVRE (LIVRÉ) : Le robot a terminé sa livraison et va disparaître
     */
    public enum EtatRobot {
        LIBRE,      // Le robot cherche un colis à transporter
        TRANSPORT,  // Le robot transporte un colis
        LIVRE       // Le robot a livré son colis et va disparaître
    }

    // État actuel du robot (libre, en transport, ou livré)
    protected EtatRobot etatActuel;

    // Le colis que le robot transporte actuellement (null si aucun)
    public ColorPackage colisTransporte;

    // Coordonnées de la destination où le robot doit livrer le colis
    protected int destinationX;
    protected int destinationY;

    // Temps de départ et d'arrivée pour mesurer la durée de livraison
    protected long momentDepart;
    protected long momentArrivee;

    // Référence vers l'environnement de simulation (la grille)
    protected ColorGridEnvironment environnement;

    /**
     * CARTE DES DESTINATIONS FINALES (GOALS)
     *
     * Cette map associe chaque identifiant de destination à ses coordonnées dans la grille.
     * Les destinations sont les endroits où les robots doivent livrer les colis.
     *
     * Dans cette simulation :
     * - Destination 1 (Z1) : position [5, 0] (en haut à gauche)
     * - Destination 2 (Z2) : position [15, 0] (en haut à droite)
     *
     * Cette map est statique car les destinations ne changent jamais pendant la simulation.
     */
    protected static final Map<Integer, int[]> DESTINATIONS = new HashMap<>();
    static {
        DESTINATIONS.put(1, new int[]{5, 0});   // Zone Z1 - Destination 1
        DESTINATIONS.put(2, new int[]{15, 0});  // Zone Z2 - Destination 2
    }

    /**
     * POSITIONS DES ZONES DE DÉPART
     *
     * Ce tableau contient les coordonnées de toutes les zones de départ où apparaissent
     * les colis à transporter. Les robots viennent chercher les colis dans ces zones.
     *
     * Les trois zones de départ sont situées en bas de la grille (ligne 19) :
     * - Zone 1 : [6, 19]
     * - Zone 2 : [9, 19]
     * - Zone 3 : [12, 19]
     */
    int[][] zonesDeDepart = { {6, 19}, {9, 19}, {12, 19} };

    /**
     * CONSTRUCTEUR DU ROBOT
     *
     * Ce constructeur crée un nouveau robot avec tous les paramètres nécessaires.
     * Il initialise l'état du robot à LIBRE (prêt à chercher un colis) et
     * lui donne une orientation aléatoire pour commencer.
     *
     * @param nom Le nom du robot (ex: "Robot0", "Robot1")
     * @param champ Le champ de vision du robot (rayon en cases)
     * @param debug Mode debug (0 = désactivé, 1 = activé)
     * @param position Position initiale du robot [x, y]
     * @param couleur Couleur d'affichage du robot
     * @param lignes Nombre de lignes de la grille
     * @param colonnes Nombre de colonnes de la grille
     * @param env L'environnement de simulation (la grille)
     * @param graine Graine pour le générateur aléatoire
     */
    public MyRobot(String nom, int champ, int debug, int[] position, Color couleur, int lignes, int colonnes, ColorGridEnvironment env, long graine) {
        // Appeler le constructeur de la classe parente
        super(nom, champ, debug, position, couleur, lignes, colonnes, graine);

        // Sauvegarder la référence vers l'environnement
        this.environnement = env;

        // Initialiser l'état du robot à LIBRE (prêt à chercher un colis)
        this.etatActuel = EtatRobot.LIBRE;

        // Aucun colis transporté au début
        this.colisTransporte = null;

        // Choisir une orientation aléatoire pour commencer
        randomOrientation();

        // Enregistrer la création du robot dans les logs
        LogManager.getInstance().logAction(nom, "Robot créé et prêt à travailler");
    }


    /**
     * MÉTHODE POUR TROUVER UNE ZONE DE DÉPART AVEC DES COLIS
     *
     * Cette méthode parcourt toutes les zones de départ pour trouver une zone
     * qui contient au moins un colis à transporter. C'est la première étape
     * pour qu'un robot libre puisse commencer une nouvelle livraison.
     *
     * Algorithme :
     * 1. Parcourir toutes les positions des zones de départ
     * 2. Pour chaque position, vérifier s'il y a une zone de départ
     * 3. Si la zone existe, vérifier si elle contient des colis
     * 4. Retourner la première zone trouvée avec des colis
     *
     * @return Une zone de départ contenant des colis, ou null si aucune trouvée
     */
    protected ColorStartZone findStartZoneWithPackage() {
        // Parcourir toutes les zones de départ connues
        for (int[] position : zonesDeDepart) {
            // Récupérer la cellule à cette position
            Cell cellule = environnement.getGrid()[position[0]][position[1]];

            // Vérifier que c'est bien une cellule colorée avec une zone de départ
            if (cellule instanceof ColorCell && cellule.getContent() instanceof ColorStartZone) {
                ColorStartZone zone = (ColorStartZone) cellule.getContent();

                // Vérifier si cette zone contient des colis
                if (!zone.getPackages().isEmpty()) {
                    return zone; // Zone trouvée avec des colis !
                }
            }
        }
        return null; // Aucune zone avec des colis trouvée
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT EST ADJACENT À UNE POSITION
     *
     * Cette méthode vérifie si le robot est directement à côté d'une position donnée.
     * "Adjacent" signifie que le robot est à exactement une case de distance
     * (horizontalement ou verticalement, pas en diagonal).
     *
     * Calcul : La distance de Manhattan entre deux points est la somme des
     * différences absolues de leurs coordonnées. Si cette distance est 1,
     * alors les points sont adjacents.
     *
     * @param ligne La coordonnée X de la position à vérifier
     * @param colonne La coordonnée Y de la position à vérifier
     * @return true si le robot est adjacent à cette position, false sinon
     */
    protected boolean isAdjacentTo(int ligne, int colonne) {
        // Calculer la distance de Manhattan (distance en cases)
        int distanceX = Math.abs(this.getX() - ligne);
        int distanceY = Math.abs(this.getY() - colonne);
        int distanceTotale = distanceX + distanceY;

        // Adjacent = distance totale de 1 case exactement
        return distanceTotale == 1;
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI UNE CELLULE EST LIBRE
     *
     * Cette méthode vérifie si une position dans la grille est libre,
     * c'est-à-dire qu'elle ne contient pas d'obstacle ou d'autre robot.
     * Une cellule libre peut être traversée par le robot.
     *
     * Une cellule est considérée comme libre si :
     * - Elle n'existe pas (null) = bord de la grille
     * - Elle existe mais ne contient rien (getContent() == null)
     *
     * @param x Coordonnée X de la cellule à vérifier
     * @param y Coordonnée Y de la cellule à vérifier
     * @return true si la cellule est libre, false si elle est occupée
     */
    private boolean isCellFree(int x, int y) {
        // Récupérer la cellule à cette position
        Cell cellule = environnement.getGrid()[x][y];

        // Une cellule est libre si elle n'existe pas ou ne contient rien
        return cellule == null || cellule.getContent() == null;
    }

    /**
     * MÉTHODE POUR CALCULER LA DISTANCE EUCLIDIENNE ENTRE DEUX POINTS
     *
     * Cette méthode calcule la distance "à vol d'oiseau" entre deux positions.
     * La distance euclidienne est la ligne droite entre deux points, calculée
     * avec le théorème de Pythagore : √((x2-x1)² + (y2-y1)²)
     *
     * Cette distance est utile pour :
     * - Évaluer quelle zone de départ est la plus proche
     * - Calculer l'efficacité d'un chemin
     * - Prendre des décisions de navigation
     *
     * @param x1 Coordonnée X du premier point
     * @param y1 Coordonnée Y du premier point
     * @param x2 Coordonnée X du deuxième point
     * @param y2 Coordonnée Y du deuxième point
     * @return La distance euclidienne entre les deux points
     */
    public double distanceTo(int x1, int y1, int x2, int y2) {
        // Calculer les différences de coordonnées
        int differenceX = x2 - x1;
        int differenceY = y2 - y1;

        // Appliquer le théorème de Pythagore
        double distanceCarree = Math.pow(differenceX, 2) + Math.pow(differenceY, 2);
        return Math.sqrt(distanceCarree);
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT EST ENCORE ACTIF
     *
     * Cette méthode indique si le robot est encore en fonctionnement dans la simulation.
     * Un robot devient inactif quand il a terminé sa livraison (état LIVRE).
     * Les robots inactifs disparaissent de la simulation.
     *
     * États actifs : LIBRE, TRANSPORT
     * État inactif : LIVRE
     *
     * @return true si le robot est actif, false s'il a terminé sa mission
     */
    public boolean isActive() {
        return etatActuel != EtatRobot.LIVRE;
    }

    /**
     *  methode pour faire avancer le robot un pas vers une destinantion
     * @param targetX
     * @param targetY
     */
    protected void moveOneStepTo(int targetX, int targetY) {
        HashMap<String, Location> directions = getNextCoordinate();
        Location bestMove = null;
        double minDist = Double.MAX_VALUE;
        // chercher la meilleure position
        for (Map.Entry<String, Location> entry : directions.entrySet()) {
            Location loc = entry.getValue();
            if (loc.getX() < 0 || loc.getX() >= rows || loc.getY() < 0 || loc.getY() >= columns) continue;

            if (!isCellFree(loc.getX(), loc.getY())) continue;

            double dist = distanceTo(loc.getX(), loc.getY(), targetX, targetY);
            if (dist < minDist) {
                minDist = dist;
                bestMove = loc;
            }
        }
        // s'orienter vers la meilleure position
        if (bestMove != null) {
            if (bestMove.getX() == this.getX() - 1) setCurrentOrientation(Orientation.up);
            if (bestMove.getX() == this.getX() + 1) setCurrentOrientation(Orientation.down);
            if (bestMove.getY() == this.getY() - 1) setCurrentOrientation(Orientation.left);
            if (bestMove.getY() == this.getY() + 1) setCurrentOrientation(Orientation.right);

            moveForward();
            // If this is a MyTransitRobot, consume battery for movement
            if (this instanceof MyTransitRobot) {
                ((MyTransitRobot)this).consumeBatteryForMovement();
            }
        }
    }


    /**
     * MÉTHODE PRINCIPALE DE LOGIQUE DU ROBOT (STEP)
     *
     * Cette méthode est appelée à chaque étape de la simulation et contient
     * toute la logique de comportement du robot. Elle implémente une machine
     * à états simple avec trois états possibles.
     *
     * Machine à états du robot :
     * 1. LIBRE : Cherche une zone de départ avec des colis
     * 2. TRANSPORT : Transporte un colis vers sa destination
     * 3. LIVRE : Mission terminée, le robot disparaît
     *
     * Cette méthode est le "cerveau" du robot qui décide quoi faire à chaque instant.
     */
    public void step() {
        // Si le robot a terminé sa mission, ne rien faire
        if (etatActuel == EtatRobot.LIVRE) return;

        // ÉTAT 1 : ROBOT LIBRE - Chercher un colis à transporter
        if (etatActuel == EtatRobot.LIBRE) {
            // Chercher une zone de départ qui contient des colis
            ColorStartZone zone = findStartZoneWithPackage();
            if (zone == null) return; // Aucune zone avec des colis trouvée

            // Vérifier si le robot est à côté de la zone
            if (isAdjacentTo(zone.getX(), zone.getY())) {
                // Le robot est adjacent à la zone, il peut prendre un colis
                if (!zone.getPackages().isEmpty()) {
                    // Étape 1 : Prendre le premier colis disponible
                    colisTransporte = zone.getPackages().get(0);
                    zone.removePackage(colisTransporte);

                    // Étape 2 : Enregistrer le moment de départ pour mesurer la performance
                    momentDepart = System.currentTimeMillis();

                    // Étape 3 : Déterminer la destination du colis
                    int[] positionDestination = DESTINATIONS.get(colisTransporte.getDestinationGoalId());
                    if (positionDestination != null) {
                        destinationX = positionDestination[0];
                        destinationY = positionDestination[1];
                        etatActuel = EtatRobot.TRANSPORT; // Changer d'état
                    }

                    // Étape 4 : Enregistrer cette action dans les logs
                    LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                        "a pris un colis de " + colisTransporte.getStartZone() + " pour la destination " + colisTransporte.getDestinationGoalId());
                }
            } else {
                // Le robot n'est pas adjacent, se déplacer vers la zone
                moveOneStepTo(zone.getX(), zone.getY());
            }

        // ÉTAT 2 : ROBOT EN TRANSPORT - Livrer le colis à destination
        } else if (etatActuel == EtatRobot.TRANSPORT) {
            // Vérifier si le robot a atteint sa destination
            if ((this.getX() == destinationX) && (this.getY() == destinationY)) {
                // LIVRAISON RÉUSSIE !

                // Étape 1 : Marquer le colis comme livré
                colisTransporte.setState(PackageState.ARRIVED);

                // Étape 2 : Incrémenter le compteur global de livraisons
                MySimFactory.deliveredCount++;

                // Étape 3 : Calculer le temps de livraison pour les statistiques
                momentArrivee = System.currentTimeMillis();
                long dureeLivraison = momentArrivee - momentDepart;

                // Étape 4 : Changer l'état du robot à LIVRE (mission terminée)
                etatActuel = EtatRobot.LIVRE;

                // Étape 5 : Faire disparaître le robot de la grille
                environnement.removeCellContent(this.getX(), this.getY());

                // Étape 6 : Enregistrer la livraison réussie dans les logs
                LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                    "a livré le colis en " + (dureeLivraison/1000) + " secondes");

            } else {
                // Le robot n'est pas encore arrivé, continuer à se déplacer
                moveOneStepTo(destinationX, destinationY);
            }
        }
    }

    /**
     * MÉTHODE POUR TRAITER LES MESSAGES REÇUS
     *
     * Cette méthode est appelée quand le robot reçoit un message d'un autre robot
     * ou du système. Dans cette implémentation de base, le robot se contente
     * d'enregistrer le message reçu dans les logs.
     *
     * Les classes dérivées (comme MyTransitRobot) peuvent redéfinir cette méthode
     * pour traiter des types de messages plus complexes.
     *
     * @param message Le message reçu
     */
    @Override
    public void handleMessage(Message message) {
        // Enregistrer la réception du message dans les logs
        LogManager.getInstance().logCoordination(getName(),
            "a reçu un message: " + message.getContent());
    }

    /**
     * MÉTHODE MOVE REQUISE PAR L'INTERFACE
     *
     * Cette méthode est requise par l'interface du simulateur. Elle délègue
     * simplement l'exécution à la méthode step() qui contient la vraie logique.
     *
     * Le paramètre step n'est pas utilisé dans cette implémentation car
     * le robot exécute toujours une seule action par appel.
     *
     * @param etape Le numéro d'étape (non utilisé dans cette implémentation)
     */
    @Override
    public void move(int etape) {
        // Déléguer à la méthode step() qui contient la logique principale
        step();
    }
}
