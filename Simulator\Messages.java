package Simulator;

/**
 * FICHIER DES MESSAGES POUR LA COMMUNICATION ENTRE ROBOTS (VERSION SIMPLIFIÉE)
 *
 * Ce fichier contient les classes essentielles pour que les robots puissent
 * communiquer entre eux dans le système d'enchères décentralisé simplifié.
 * Version adaptée pour un projet étudiant réaliste.
 *
 * Types de messages simplifiés :
 * - Bid : Une offre qu'un robot fait pour obtenir une tâche
 * - BidMessage : Message contenant une offre
 * - NewTaskMessage : Message pour annoncer une nouvelle tâche
 * - TaskAssignedMessage : Message pour notifier l'assignation d'une tâche
 * - TransitZoneStatusMessage : Message pour l'état des zones de transit
 * - TaskAssignedMessage : Message pour dire qu'une tâche est attribuée
 * - TaskCompletedMessage : Message pour dire qu'une tâche est terminée
 *
 * Ce système permet aux robots de travailler ensemble de manière autonome.
 */

/**
 * CLASSE ENCHERE (BID) - REPRÉSENTE UNE OFFRE POUR UNE TÂCHE
 *
 * Cette classe représente une offre qu'un robot fait pour obtenir une tâche.
 * Dans le système d'enchères, chaque robot peut faire une offre pour une tâche
 * en proposant un "montant" qui représente à quel point il est bien placé
 * pour faire cette tâche.
 *
 * Comment ça marche :
 * - Plus le montant est élevé, plus le robot est motivé/capable de faire la tâche
 * - Le robot qui fait la meilleure offre obtient la tâche
 * - Si deux robots font la même offre, celui qui l'a faite en premier gagne
 *
 * Cette classe implémente Comparable pour pouvoir comparer les offres facilement.
 */
class Enchere implements Comparable<Enchere> {
    // Identifiant du robot qui fait l'offre
    private String identifiantRobot;

    // Identifiant de la tâche pour laquelle on fait l'offre
    private String identifiantTache;

    // Montant de l'offre (plus c'est haut, mieux c'est)
    private double montantOffre;

    // Moment où l'offre a été créée (pour départager les égalités)
    private long horodatage;

    /**
     * CONSTRUCTEUR POUR CRÉER UNE NOUVELLE OFFRE
     *
     * Ce constructeur crée une nouvelle offre avec les informations données.
     * Il enregistre automatiquement l'heure actuelle pour pouvoir départager
     * les offres identiques (la première offre gagne).
     *
     * @param idRobot L'identifiant du robot qui fait l'offre
     * @param idTache L'identifiant de la tâche pour laquelle on fait l'offre
     * @param montant Le montant de l'offre (plus c'est élevé, mieux c'est)
     */
    public Enchere(String idRobot, String idTache, double montant) {
        this.identifiantRobot = idRobot;
        this.identifiantTache = idTache;
        this.montantOffre = montant;
        // Enregistrer l'heure actuelle en millisecondes
        this.horodatage = System.currentTimeMillis();

        // Enregistrer la création de l'offre dans les logs
        LogManager.getInstance().logCoordination(idRobot,
            "Offre créée pour la tâche " + idTache + " avec montant " + String.format("%.2f", montant));
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DU ROBOT
     *
     * Cette méthode retourne l'identifiant du robot qui a fait cette offre.
     * C'est utile pour savoir qui a proposé quoi dans les enchères.
     *
     * @return L'identifiant du robot qui fait l'offre
     */
    public String getRobotId() {
        return identifiantRobot;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DE LA TÂCHE
     *
     * Cette méthode retourne l'identifiant de la tâche pour laquelle
     * cette offre a été faite. Permet de savoir de quelle tâche on parle.
     *
     * @return L'identifiant de la tâche concernée par l'offre
     */
    public String getTaskId() {
        return identifiantTache;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE MONTANT DE L'OFFRE
     *
     * Cette méthode retourne le montant proposé dans cette offre.
     * Plus le montant est élevé, plus le robot est motivé/capable
     * de faire la tâche.
     *
     * @return Le montant de l'offre
     */
    public double getBidAmount() {
        return montantOffre;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'HORODATAGE DE L'OFFRE
     *
     * Cette méthode retourne le moment où l'offre a été créée.
     * C'est utilisé pour départager les offres identiques :
     * la première offre faite gagne.
     *
     * @return L'horodatage en millisecondes
     */
    public long getTimestamp() {
        return horodatage;
    }

    /**
     * MÉTHODE POUR COMPARER DEUX OFFRES
     *
     * Cette méthode compare cette offre avec une autre pour déterminer
     * laquelle est la meilleure. Elle suit ces règles :
     * 1. L'offre avec le montant le plus élevé gagne
     * 2. Si les montants sont égaux, l'offre faite en premier gagne
     *
     * Cette méthode est requise par l'interface Comparable et permet
     * de trier automatiquement les offres de la meilleure à la moins bonne.
     *
     * @param autreOffre L'autre offre à comparer avec celle-ci
     * @return Un nombre négatif si cette offre est meilleure, positif si elle est moins bonne, 0 si égales
     */
    @Override
    public int compareTo(Enchere autreOffre) {
        // Étape 1 : Comparer les montants (plus élevé = meilleur)
        int comparaisonMontant = Double.compare(autreOffre.montantOffre, this.montantOffre);
        if (comparaisonMontant != 0) {
            // Les montants sont différents, retourner le résultat de la comparaison
            return comparaisonMontant;
        }

        // Étape 2 : Si les montants sont égaux, comparer les horodatages (plus tôt = meilleur)
        return Long.compare(this.horodatage, autreOffre.horodatage);
    }

    /**
     * MÉTHODE POUR CONVERTIR L'OFFRE EN TEXTE
     *
     * Cette méthode crée une représentation textuelle de l'offre
     * qui est facile à lire et utile pour le débogage.
     *
     * @return Une chaîne de caractères décrivant l'offre
     */
    @Override
    public String toString() {
        return "Enchere{" +
                "robot='" + identifiantRobot + '\'' +
                ", tache='" + identifiantTache + '\'' +
                ", montant=" + String.format("%.2f", montantOffre) +
                '}';
    }
}

/**
 * CLASSE DE BASE POUR TOUS LES MESSAGES DU SYSTÈME D'ENCHÈRES DÉCENTRALISÉ
 *
 * Cette classe abstraite définit la structure commune à tous les messages
 * que les robots s'envoient entre eux dans le système d'enchères décentralisé.
 *
 * Dans ce système, les robots communiquent constamment pour :
 * - Faire des offres (enchères) sur les tâches disponibles
 * - Annoncer de nouvelles tâches à accomplir
 * - Partager des informations sur l'état des zones de transit
 * - Échanger des données de performance et d'efficacité
 * - Coordonner leurs actions pour éviter les conflits
 *
 * Chaque message contient trois informations essentielles :
 * 1. L'expéditeur : quel robot a envoyé le message
 * 2. Le type : quel genre de message (offre, acceptation, refus, etc.)
 * 3. L'horodatage : quand le message a été créé
 *
 * Cette classe est abstraite car elle ne peut pas être utilisée directement.
 * Il faut créer des classes spécialisées pour chaque type de message spécifique.
 */
abstract class Message {
    // Le robot qui a envoyé ce message
    private MyTransitRobot expediteur;

    // Le moment où le message a été créé (en millisecondes depuis 1970)
    private long horodatage;

    // Le type de message (ex: "OFFRE", "ACCEPTATION", "REFUS", "NOUVELLE_TACHE")
    private String typeMessage;

    /**
     * CONSTRUCTEUR POUR CRÉER UN NOUVEAU MESSAGE
     *
     * Ce constructeur initialise les informations de base communes à tous les messages.
     * Il enregistre automatiquement l'heure actuelle pour pouvoir :
     * - Ordonner les messages par ordre chronologique
     * - Détecter les messages trop anciens
     * - Mesurer les délais de communication
     *
     * @param expediteur Le robot qui envoie le message
     * @param typeMessage Le type de message (ex: "OFFRE", "ACCEPTATION")
     */
    public Message(MyTransitRobot expediteur, String typeMessage) {
        this.expediteur = expediteur;
        this.typeMessage = typeMessage;
        // Enregistrer l'heure actuelle en millisecondes depuis le 1er janvier 1970
        this.horodatage = System.currentTimeMillis();

        // Enregistrer la création du message dans les logs pour le débogage
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Création d'un message de type " + typeMessage);
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'EXPÉDITEUR DU MESSAGE
     *
     * Cette méthode retourne le robot qui a envoyé ce message.
     * C'est utile pour savoir qui a fait une offre, qui annonce une tâche, etc.
     *
     * @return Le robot expéditeur du message
     */
    public MyTransitRobot getSender() {
        return expediteur;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'HORODATAGE DU MESSAGE
     *
     * Cette méthode retourne le moment où le message a été créé.
     * C'est utile pour :
     * - Ordonner les messages par ordre chronologique
     * - Ignorer les messages trop anciens
     * - Mesurer les délais de communication
     *
     * @return L'horodatage en millisecondes depuis le 1er janvier 1970
     */
    public long getTimestamp() {
        return horodatage;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE TYPE DU MESSAGE
     *
     * Cette méthode retourne le type de message, ce qui permet au robot
     * receveur de savoir comment traiter le message.
     *
     * @return Le type de message (chaîne de caractères)
     */
    public String getMessageType() {
        return typeMessage;
    }

    /**
     * MÉTHODE ABSTRAITE POUR SÉRIALISER LE MESSAGE
     *
     * Cette méthode doit être implémentée par chaque type de message spécifique.
     * Elle convertit le message en une chaîne de caractères qui peut être
     * transmise sur le réseau entre les robots.
     *
     * Format typique : "TYPE|donnee1|donnee2|donnee3"
     *
     * @return Le message sous forme de chaîne de caractères
     */
    public abstract String serialize();

    /**
     * MÉTHODE ABSTRAITE POUR TRAITER LE MESSAGE
     *
     * Cette méthode doit être implémentée par chaque type de message spécifique.
     * Elle définit ce qui se passe quand un robot reçoit ce type de message.
     *
     * @param destinataire Le robot qui reçoit et doit traiter le message
     */
    public abstract void process(MyTransitRobot destinataire);


}

/**
 * CLASSE DE MESSAGE SIMPLE POUR LA COMMUNICATION BASIQUE ENTRE ROBOTS
 *
 * Cette classe représente un message simple qui peut être utilisé pour
 * des communications basiques entre robots quand on n'a pas besoin de
 * la complexité des messages spécialisés du système d'enchères.
 *
 * Utilisation typique :
 * - Messages de débogage entre robots
 * - Communications informelles pendant le développement
 * - Tests de connectivité entre robots
 * - Échanges d'informations simples qui ne nécessitent pas de traitement spécial
 *
 * Ce type de message est plus simple que les messages du système d'enchères
 * car il ne contient que du texte libre sans structure particulière.
 */
class SimpleMessage {
    // Le robot qui a envoyé ce message simple
    private MyTransitRobot expediteur;

    // Le contenu textuel du message (texte libre)
    private String contenu;

    /**
     * CONSTRUCTEUR POUR CRÉER UN NOUVEAU MESSAGE SIMPLE
     *
     * Ce constructeur crée un message simple avec un expéditeur et un contenu.
     * Contrairement aux messages spécialisés, il n'y a pas de validation
     * particulière du contenu - c'est du texte libre.
     *
     * @param expediteur Le robot qui envoie le message
     * @param contenu Le contenu textuel du message
     */
    public SimpleMessage(MyTransitRobot expediteur, String contenu) {
        // Étape 1 : Sauvegarder l'expéditeur du message
        this.expediteur = expediteur;

        // Étape 2 : Sauvegarder le contenu du message
        this.contenu = contenu;

        // Étape 3 : Enregistrer la création du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Création d'un message simple : " + contenu);
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'EXPÉDITEUR DU MESSAGE
     *
     * Cette méthode retourne le robot qui a envoyé ce message simple.
     * C'est utile pour savoir qui a envoyé quoi dans les communications.
     *
     * @return Le robot qui a envoyé le message
     */
    public MyTransitRobot getSender() {
        return expediteur;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE CONTENU DU MESSAGE
     *
     * Cette méthode retourne le contenu textuel du message.
     * Le contenu peut être n'importe quel texte libre selon les besoins.
     *
     * @return Le contenu textuel du message
     */
    public String getContent() {
        return contenu;
    }
}

/**
 * MESSAGE CONTENANT UNE OFFRE D'ENCHÈRE
 *
 * Cette classe représente un message qu'un robot envoie pour faire une offre
 * sur une tâche. Quand un robot veut obtenir une tâche, il crée une offre
 * (classe Enchere) et l'envoie aux autres robots dans ce type de message.
 *
 * Le message contient :
 * - L'offre elle-même (montant, robot, tâche)
 * - Les informations du message (expéditeur, horodatage)
 * - Les méthodes pour sérialiser/désérialiser le message
 */
class MessageOffre extends Message {
    // L'offre contenue dans ce message
    private Enchere offre;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "OFFRE";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE D'OFFRE
     *
     * Ce constructeur crée un nouveau message contenant une offre.
     * Il appelle le constructeur de la classe parente (Message) pour
     * initialiser les informations de base du message.
     *
     * @param expediteur Le robot qui envoie le message
     * @param offre L'offre à transmettre dans le message
     */
    public MessageOffre(MyTransitRobot expediteur, Enchere offre) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.offre = offre;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Envoi d'une offre pour la tâche " + offre.getTaskId());
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'OFFRE CONTENUE DANS LE MESSAGE
     *
     * Cette méthode permet d'accéder à l'offre qui est transportée
     * par ce message. C'est utile pour traiter l'offre une fois
     * que le message est reçu.
     *
     * @return L'offre contenue dans le message
     */
    public Enchere getBid() {
        return offre;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau. Elle inclut toutes les
     * informations nécessaires pour reconstruire le message plus tard.
     *
     * Format : TYPE|robotId|taskId|bidAmount|timestamp
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" +
               offre.getRobotId() + "|" +
               offre.getTaskId() + "|" +
               offre.getBidAmount() + "|" +
               offre.getTimestamp();
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un MessageOffre complet. C'est utilisé quand
     * un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau MessageOffre, ou null si le texte est invalide
     */
    public static MessageOffre deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct
        if (parties.length < 5 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire les informations de l'offre
        String idRobot = parties[1];
        String idTache = parties[2];
        double montantOffre = Double.parseDouble(parties[3]);

        // Recréer l'offre et le message
        Enchere offre = new Enchere(idRobot, idTache, montantOffre);
        return new MessageOffre(expediteur, offre);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle transmet l'offre au coordinateur de tâches du
     * robot receveur pour qu'il puisse l'évaluer.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Transmettre l'offre au coordinateur de tâches pour évaluation
        destinataire.getCoordinateurTaches().handleBid(offre);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Offre reçue de " + offre.getRobotId() + " pour la tâche " + offre.getTaskId());
    }
}

/**
 * MESSAGE DE NOTIFICATION D'ACCEPTATION D'OFFRE
 *
 * Cette classe représente un message qu'un robot envoie pour notifier
 * qu'une offre (enchère) a été acceptée pour une tâche spécifique.
 * Ce message est envoyé par le robot qui a organisé l'enchère vers
 * le robot gagnant pour lui confirmer qu'il a obtenu la tâche.
 *
 * Contexte d'utilisation :
 * 1. Plusieurs robots font des offres sur une tâche
 * 2. Le délai d'enchère expire
 * 3. Le robot organisateur détermine le gagnant
 * 4. Il envoie ce message au robot gagnant pour confirmer l'attribution
 * 5. Le robot gagnant peut alors commencer à exécuter la tâche
 *
 * Ce message fait partie du protocole de communication du système d'enchères décentralisé.
 */
class BidAcceptedMessage extends Message {
    // L'identifiant de la tâche pour laquelle l'offre a été acceptée
    private String identifiantTache;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "BID_ACCEPTED";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE D'ACCEPTATION D'OFFRE
     *
     * Ce constructeur crée un nouveau message pour notifier qu'une offre
     * a été acceptée. Il appelle le constructeur de la classe parente
     * pour initialiser les informations de base du message.
     *
     * @param expediteur Le robot qui envoie la notification d'acceptation
     * @param identifiantTache L'identifiant de la tâche pour laquelle l'offre est acceptée
     */
    public BidAcceptedMessage(MyTransitRobot expediteur, String identifiantTache) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.identifiantTache = identifiantTache;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Envoi d'une acceptation d'offre pour la tâche " + identifiantTache);
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DE LA TÂCHE
     *
     * Cette méthode retourne l'identifiant de la tâche pour laquelle
     * l'offre a été acceptée. C'est utile pour que le robot receveur
     * sache de quelle tâche il s'agit.
     *
     * @return L'identifiant de la tâche concernée
     */
    public String getTaskId() {
        return identifiantTache;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau entre les robots.
     *
     * Format : TYPE|taskId
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" + identifiantTache;
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un BidAcceptedMessage complet. C'est utilisé
     * quand un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau BidAcceptedMessage, ou null si le texte est invalide
     */
    public static BidAcceptedMessage deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct
        if (parties.length < 2 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire l'identifiant de la tâche
        String identifiantTache = parties[1];

        // Recréer le message
        return new BidAcceptedMessage(expediteur, identifiantTache);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle notifie le coordinateur de tâches que l'offre
     * a été acceptée et que la tâche lui est maintenant assignée.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Notifier le coordinateur que l'offre a été acceptée
        destinataire.getCoordinateurTaches().handleBidAccepted(identifiantTache, getSender().getName());

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Offre acceptée reçue pour la tâche " + identifiantTache);
    }
}

/**
 * MESSAGE DE NOTIFICATION DE REJET D'OFFRE
 *
 * Cette classe représente un message qu'un robot envoie pour notifier
 * qu'une offre (enchère) a été rejetée pour une tâche spécifique.
 * Ce message est envoyé par le robot qui a organisé l'enchère vers
 * les robots perdants pour leur indiquer qu'ils n'ont pas obtenu la tâche.
 *
 * Contexte d'utilisation :
 * 1. Plusieurs robots font des offres sur une tâche
 * 2. Le délai d'enchère expire
 * 3. Le robot organisateur détermine le gagnant
 * 4. Il envoie ce message aux robots perdants pour les informer
 * 5. Les robots perdants peuvent alors chercher d'autres tâches
 *
 * Le message contient aussi l'identifiant du robot gagnant pour information.
 * Cela permet aux robots de connaître les performances de leurs concurrents.
 */
class BidRejectedMessage extends Message {
    // L'identifiant de la tâche pour laquelle l'offre a été rejetée
    private String identifiantTache;

    // L'identifiant du robot qui a gagné l'enchère
    private String identifiantRobotGagnant;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "BID_REJECTED";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE DE REJET D'OFFRE
     *
     * Ce constructeur crée un nouveau message pour notifier qu'une offre
     * a été rejetée. Il inclut l'information sur qui a gagné l'enchère
     * pour que le robot perdant puisse analyser la concurrence.
     *
     * @param expediteur Le robot qui envoie la notification de rejet
     * @param identifiantTache L'identifiant de la tâche pour laquelle l'offre est rejetée
     * @param identifiantRobotGagnant L'identifiant du robot qui a gagné l'enchère
     */
    public BidRejectedMessage(MyTransitRobot expediteur, String identifiantTache, String identifiantRobotGagnant) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.identifiantTache = identifiantTache;
        this.identifiantRobotGagnant = identifiantRobotGagnant;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Envoi d'un rejet d'offre pour la tâche " + identifiantTache +
            " (gagnant: " + identifiantRobotGagnant + ")");
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DE LA TÂCHE
     *
     * Cette méthode retourne l'identifiant de la tâche pour laquelle
     * l'offre a été rejetée. C'est utile pour que le robot receveur
     * sache de quelle tâche il s'agit.
     *
     * @return L'identifiant de la tâche concernée
     */
    public String getTaskId() {
        return identifiantTache;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DU ROBOT GAGNANT
     *
     * Cette méthode retourne l'identifiant du robot qui a gagné l'enchère.
     * C'est utile pour que le robot perdant puisse analyser la concurrence
     * et ajuster ses futures stratégies d'enchères.
     *
     * @return L'identifiant du robot qui a gagné l'enchère
     */
    public String getWinningRobotId() {
        return identifiantRobotGagnant;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau entre les robots.
     *
     * Format : TYPE|taskId|winningRobotId
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" + identifiantTache + "|" + identifiantRobotGagnant;
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un BidRejectedMessage complet. C'est utilisé
     * quand un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau BidRejectedMessage, ou null si le texte est invalide
     */
    public static BidRejectedMessage deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct
        if (parties.length < 3 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire les informations du message
        String identifiantTache = parties[1];
        String identifiantRobotGagnant = parties[2];

        // Recréer le message
        return new BidRejectedMessage(expediteur, identifiantTache, identifiantRobotGagnant);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle notifie le coordinateur de tâches que l'offre
     * a été rejetée et que la tâche a été assignée à un autre robot.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Notifier le coordinateur que l'offre a été rejetée
        destinataire.getCoordinateurTaches().handleBidRejected(identifiantTache, identifiantRobotGagnant);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Offre rejetée reçue pour la tâche " + identifiantTache +
            " (gagnant: " + identifiantRobotGagnant + ")");
    }
}

/**
 * MESSAGE D'ANNONCE D'UNE NOUVELLE TÂCHE DISPONIBLE
 *
 * Cette classe représente un message qu'un robot envoie pour annoncer
 * qu'une nouvelle tâche est disponible et que les autres robots peuvent
 * faire des offres (enchères) pour l'obtenir.
 *
 * Contexte d'utilisation :
 * 1. Un nouveau colis apparaît dans une zone de départ
 * 2. Le robot qui le détecte crée une tâche correspondante
 * 3. Il diffuse ce message à tous les autres robots
 * 4. Les robots intéressés peuvent alors faire des offres
 * 5. Le système d'enchères détermine qui obtiendra la tâche
 *
 * Ce message contient toutes les informations nécessaires pour que les robots
 * puissent évaluer s'ils sont intéressés par la tâche et calculer leur offre.
 */
class NewTaskMessage extends Message {
    // La tâche qui est annoncée dans ce message
    private CoordinateurTaches.Task tache;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "NEW_TASK";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE D'ANNONCE DE NOUVELLE TÂCHE
     *
     * Ce constructeur crée un nouveau message pour annoncer qu'une tâche
     * est disponible. Il appelle le constructeur de la classe parente
     * pour initialiser les informations de base du message.
     *
     * @param expediteur Le robot qui annonce la nouvelle tâche
     * @param tache La tâche qui est annoncée
     */
    public NewTaskMessage(MyTransitRobot expediteur, CoordinateurTaches.Task tache) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.tache = tache;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Annonce d'une nouvelle tâche : " + tache.getId());
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LA TÂCHE ANNONCÉE
     *
     * Cette méthode retourne la tâche qui est annoncée dans ce message.
     * C'est utile pour que les robots receveurs puissent analyser la tâche
     * et décider s'ils veulent faire une offre.
     *
     * @return La tâche annoncée
     */
    public CoordinateurTaches.Task getTask() {
        return tache;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau entre les robots.
     * Elle inclut toutes les informations de la tâche.
     *
     * Format : TYPE|taskId|startX|startY|goalX|goalY
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" +
               tache.getId() + "|" +
               tache.getStartX() + "|" +
               tache.getStartY() + "|" +
               tache.getGoalX() + "|" +
               tache.getGoalY();
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un NewTaskMessage complet avec sa tâche.
     * C'est utilisé quand un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau NewTaskMessage, ou null si le texte est invalide
     */
    public static NewTaskMessage deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct (6 parties attendues)
        if (parties.length < 6 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire les informations de la tâche
        String identifiantTache = parties[1];
        int coordonneeXDepart = Integer.parseInt(parties[2]);
        int coordonneeYDepart = Integer.parseInt(parties[3]);
        int coordonneeXDestination = Integer.parseInt(parties[4]);
        int coordonneeYDestination = Integer.parseInt(parties[5]);

        // Recréer la tâche (sans référence au colis car elle n'est pas transmise)
        CoordinateurTaches.Task tacheReconstruite = new CoordinateurTaches.Task(
            identifiantTache, coordonneeXDepart, coordonneeYDepart,
            coordonneeXDestination, coordonneeYDestination, null);

        // Recréer le message
        return new NewTaskMessage(expediteur, tacheReconstruite);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle transmet la nouvelle tâche au coordinateur de tâches
     * qui décidera si le robot doit faire une offre.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Transmettre la nouvelle tâche au coordinateur pour évaluation
        destinataire.getCoordinateurTaches().handleNewTask(tache);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Nouvelle tâche reçue : " + tache.getId());
    }
}

/**
 * MESSAGE DE NOTIFICATION D'ASSIGNATION DE TÂCHE
 *
 * Cette classe représente un message qu'un robot envoie pour notifier
 * qu'une tâche a été officiellement assignée à un robot spécifique.
 * Ce message est diffusé à tous les robots pour qu'ils mettent à jour
 * leur connaissance de l'état des tâches dans le système.
 *
 * Contexte d'utilisation :
 * 1. Une enchère se termine et un robot gagnant est déterminé
 * 2. Le robot organisateur assigne officiellement la tâche au gagnant
 * 3. Il diffuse ce message à tous les robots du système
 * 4. Tous les robots mettent à jour leur base de connaissances
 * 5. Cela évite que d'autres robots fassent des offres sur cette tâche
 *
 * Ce message est important pour maintenir la cohérence du système décentralisé.
 */
class TaskAssignedMessage extends Message {
    // L'identifiant de la tâche qui a été assignée
    private String identifiantTache;

    // L'identifiant du robot à qui la tâche a été assignée
    private String identifiantRobotAssigne;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "TASK_ASSIGNED";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE D'ASSIGNATION DE TÂCHE
     *
     * Ce constructeur crée un nouveau message pour notifier qu'une tâche
     * a été officiellement assignée à un robot. Il appelle le constructeur
     * de la classe parente pour initialiser les informations de base.
     *
     * @param expediteur Le robot qui envoie la notification d'assignation
     * @param identifiantTache L'identifiant de la tâche qui a été assignée
     * @param identifiantRobotAssigne L'identifiant du robot à qui la tâche est assignée
     */
    public TaskAssignedMessage(MyTransitRobot expediteur, String identifiantTache, String identifiantRobotAssigne) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.identifiantTache = identifiantTache;
        this.identifiantRobotAssigne = identifiantRobotAssigne;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Notification d'assignation : tâche " + identifiantTache +
            " assignée à " + identifiantRobotAssigne);
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DE LA TÂCHE ASSIGNÉE
     *
     * Cette méthode retourne l'identifiant de la tâche qui a été assignée.
     * C'est utile pour que les robots receveurs sachent de quelle tâche il s'agit.
     *
     * @return L'identifiant de la tâche assignée
     */
    public String getTaskId() {
        return identifiantTache;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DU ROBOT ASSIGNÉ
     *
     * Cette méthode retourne l'identifiant du robot à qui la tâche a été assignée.
     * C'est utile pour que les autres robots sachent qui s'occupe de cette tâche.
     *
     * @return L'identifiant du robot assigné à la tâche
     */
    public String getAssignedRobotId() {
        return identifiantRobotAssigne;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau entre les robots.
     *
     * Format : TYPE|taskId|assignedRobotId
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" + identifiantTache + "|" + identifiantRobotAssigne;
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un TaskAssignedMessage complet. C'est utilisé
     * quand un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau TaskAssignedMessage, ou null si le texte est invalide
     */
    public static TaskAssignedMessage deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct
        if (parties.length < 3 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire les informations du message
        String identifiantTache = parties[1];
        String identifiantRobotAssigne = parties[2];

        // Recréer le message
        return new TaskAssignedMessage(expediteur, identifiantTache, identifiantRobotAssigne);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle notifie le coordinateur de tâches que la tâche
     * a été assignée pour qu'il mette à jour sa base de connaissances.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Notifier le coordinateur de l'assignation de la tâche
        destinataire.getCoordinateurTaches().handleTaskAssigned(identifiantTache, identifiantRobotAssigne);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Assignation reçue : tâche " + identifiantTache +
            " assignée à " + identifiantRobotAssigne);
    }
}

/**
 * MESSAGE DE NOTIFICATION DE TÂCHE TERMINÉE
 *
 * Cette classe représente un message qu'un robot envoie pour notifier
 * qu'il a terminé une tâche avec succès. Ce message contient des informations
 * de performance qui permettent aux autres robots d'évaluer l'efficacité
 * du robot qui a terminé la tâche.
 *
 * Contexte d'utilisation :
 * 1. Un robot termine une livraison avec succès
 * 2. Il calcule ses statistiques de performance (temps, batterie)
 * 3. Il diffuse ce message à tous les autres robots
 * 4. Les autres robots mettent à jour leurs données d'efficacité
 * 5. Ces données influencent les futures décisions d'enchères
 *
 * Ce message est crucial pour l'apprentissage et l'optimisation du système décentralisé.
 */
class TaskCompletedMessage extends Message {
    // L'identifiant de la tâche qui a été terminée
    private String identifiantTache;

    // Le temps pris pour terminer la tâche (en millisecondes)
    private long tempsDeLivraison;

    // Le pourcentage de batterie utilisé pour cette tâche
    private double batterieUtilisee;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "TASK_COMPLETED";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE DE TÂCHE TERMINÉE
     *
     * Ce constructeur crée un nouveau message pour notifier qu'une tâche
     * a été terminée avec succès. Il inclut toutes les statistiques de
     * performance nécessaires pour l'évaluation de l'efficacité.
     *
     * @param expediteur Le robot qui envoie la notification
     * @param identifiantTache L'identifiant de la tâche terminée
     * @param tempsDeLivraison Le temps pris pour terminer la tâche (en millisecondes)
     * @param batterieUtilisee Le pourcentage de batterie utilisé
     */
    public TaskCompletedMessage(MyTransitRobot expediteur, String identifiantTache,
                               long tempsDeLivraison, double batterieUtilisee) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.identifiantTache = identifiantTache;
        this.tempsDeLivraison = tempsDeLivraison;
        this.batterieUtilisee = batterieUtilisee;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Notification de tâche terminée : " + identifiantTache +
            " en " + (tempsDeLivraison/1000) + "s");
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DE LA TÂCHE
     *
     * Cette méthode retourne l'identifiant de la tâche qui a été terminée.
     * C'est utile pour marquer la tâche comme terminée dans les bases de données.
     *
     * @return L'identifiant de la tâche terminée
     */
    public String getTaskId() {
        return identifiantTache;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE TEMPS DE LIVRAISON
     *
     * Cette méthode retourne le temps pris pour terminer la tâche.
     * Cette information est utilisée pour calculer l'efficacité du robot.
     *
     * @return Le temps de livraison en millisecondes
     */
    public long getDeliveryTime() {
        return tempsDeLivraison;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LA BATTERIE UTILISÉE
     *
     * Cette méthode retourne le pourcentage de batterie utilisé pour la tâche.
     * Cette information est utilisée pour évaluer l'efficacité énergétique.
     *
     * @return Le pourcentage de batterie utilisé
     */
    public double getBatteryUsed() {
        return batterieUtilisee;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau entre les robots.
     *
     * Format : TYPE|taskId|deliveryTime|batteryUsed
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" + identifiantTache + "|" + tempsDeLivraison + "|" + batterieUtilisee;
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un TaskCompletedMessage complet. C'est utilisé
     * quand un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau TaskCompletedMessage, ou null si le texte est invalide
     */
    public static TaskCompletedMessage deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct (4 parties attendues)
        if (parties.length < 4 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire les informations du message
        String identifiantTache = parties[1];
        long tempsDeLivraison = Long.parseLong(parties[2]);
        double batterieUtilisee = Double.parseDouble(parties[3]);

        // Recréer le message
        return new TaskCompletedMessage(expediteur, identifiantTache, tempsDeLivraison, batterieUtilisee);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle transmet les données de performance au coordinateur
     * pour qu'il mette à jour ses statistiques d'efficacité.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Transmettre les données de performance au coordinateur
        // L'expéditeur du message est le robot qui a terminé la tâche
        destinataire.getCoordinateurTaches().handleTaskCompleted(getSender().getName(), identifiantTache,
                                                                 tempsDeLivraison, batterieUtilisee);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Tâche terminée reçue : " + identifiantTache + " par " + getSender().getName());
    }
}

/**
 * MESSAGE DE PARTAGE DE MÉTRIQUES DE PERFORMANCE ENTRE ROBOTS
 *
 * Cette classe représente un message qu'un robot envoie pour partager
 * les métriques de performance d'un robot (souvent lui-même) avec tous
 * les autres robots du système. Ces informations permettent à chaque robot
 * de maintenir une vue à jour de l'efficacité de tous les autres robots.
 *
 * Contexte d'utilisation :
 * 1. Un robot termine une tâche et calcule son nouveau score d'efficacité
 * 2. Il diffuse ce message pour partager ses nouvelles statistiques
 * 3. Tous les autres robots mettent à jour leur base de connaissances
 * 4. Ces données influencent les futures décisions d'enchères
 * 5. Cela permet un équilibrage automatique de la charge de travail
 *
 * Ce message est essentiel pour l'auto-optimisation du système décentralisé.
 */
class EfficiencyUpdateMessage extends Message {
    // L'identifiant du robot dont on partage l'efficacité
    private String identifiantRobot;

    // Le score d'efficacité calculé pour ce robot
    private double scoreEfficacite;

    // Le nombre total de livraisons effectuées par ce robot
    private int nombreLivraisons;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "EFFICIENCY_UPDATE";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE DE MISE À JOUR D'EFFICACITÉ
     *
     * Ce constructeur crée un nouveau message pour partager les métriques
     * de performance d'un robot. Il appelle le constructeur de la classe
     * parente pour initialiser les informations de base du message.
     *
     * @param expediteur Le robot qui envoie la mise à jour
     * @param identifiantRobot L'identifiant du robot dont on partage l'efficacité
     * @param scoreEfficacite Le score d'efficacité calculé
     * @param nombreLivraisons Le nombre total de livraisons effectuées
     */
    public EfficiencyUpdateMessage(MyTransitRobot expediteur, String identifiantRobot,
                                  double scoreEfficacite, int nombreLivraisons) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.identifiantRobot = identifiantRobot;
        this.scoreEfficacite = scoreEfficacite;
        this.nombreLivraisons = nombreLivraisons;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logCoordination(expediteur.getName(),
            "Partage d'efficacité : " + identifiantRobot + " = " +
            String.format("%.2f", scoreEfficacite) + " (" + nombreLivraisons + " livraisons)");
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER L'IDENTIFIANT DU ROBOT
     *
     * Cette méthode retourne l'identifiant du robot dont on partage
     * les métriques d'efficacité. C'est utile pour associer les données
     * au bon robot dans la base de connaissances.
     *
     * @return L'identifiant du robot concerné
     */
    public String getRobotId() {
        return identifiantRobot;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE SCORE D'EFFICACITÉ
     *
     * Cette méthode retourne le score d'efficacité calculé pour le robot.
     * Ce score est basé sur le temps de livraison et la consommation de batterie.
     *
     * @return Le score d'efficacité (plus élevé = plus efficace)
     */
    public double getEfficiencyScore() {
        return scoreEfficacite;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE NOMBRE DE LIVRAISONS
     *
     * Cette méthode retourne le nombre total de livraisons effectuées
     * par le robot. Cette information est utilisée pour l'équilibrage
     * de la charge de travail entre les robots.
     *
     * @return Le nombre total de livraisons effectuées
     */
    public int getDeliveryCount() {
        return nombreLivraisons;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau entre les robots.
     *
     * Format : TYPE|robotId|efficiencyScore|deliveryCount
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" + identifiantRobot + "|" + scoreEfficacite + "|" + nombreLivraisons;
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un EfficiencyUpdateMessage complet. C'est utilisé
     * quand un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau EfficiencyUpdateMessage, ou null si le texte est invalide
     */
    public static EfficiencyUpdateMessage deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct (4 parties attendues)
        if (parties.length < 4 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire les informations du message
        String identifiantRobot = parties[1];
        double scoreEfficacite = Double.parseDouble(parties[2]);
        int nombreLivraisons = Integer.parseInt(parties[3]);

        // Recréer le message
        return new EfficiencyUpdateMessage(expediteur, identifiantRobot, scoreEfficacite, nombreLivraisons);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle transmet les métriques d'efficacité au coordinateur
     * pour qu'il mette à jour sa base de connaissances sur les autres robots.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Transmettre les métriques d'efficacité au coordinateur
        destinataire.getCoordinateurTaches().handleEfficiencyUpdate(identifiantRobot, scoreEfficacite, nombreLivraisons);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Efficacité reçue : " + identifiantRobot + " = " +
            String.format("%.2f", scoreEfficacite) + " (" + nombreLivraisons + " livraisons)");
    }
}

/**
 * MESSAGE D'INFORMATION SUR L'ÉTAT DES ZONES DE TRANSIT
 *
 * Cette classe représente un message qu'un robot envoie pour informer
 * les autres robots de l'état d'une zone de transit (pleine ou disponible).
 * Ces informations permettent aux robots de prendre de meilleures décisions
 * sur l'utilisation des zones de transit pour leurs livraisons.
 *
 * Contexte d'utilisation :
 * 1. Un robot arrive à une zone de transit et observe son état
 * 2. Il diffuse cette information à tous les autres robots
 * 3. Les autres robots mettent à jour leur connaissance des zones de transit
 * 4. Cela influence leurs futures décisions de routage
 * 5. Permet d'éviter la congestion dans les zones de transit
 *
 * Ce message contribue à l'optimisation globale du trafic dans l'entrepôt.
 */
class TransitZoneStatusMessage extends Message {
    // Coordonnée X de la zone de transit concernée
    private int coordonneeXTransit;

    // Coordonnée Y de la zone de transit concernée
    private int coordonneeYTransit;

    // État de la zone : true si pleine, false si disponible
    private boolean estPleine;

    // Type de message pour l'identifier lors de la transmission
    private static final String TYPE_MESSAGE = "TRANSIT_ZONE_STATUS";

    /**
     * CONSTRUCTEUR POUR CRÉER UN MESSAGE D'ÉTAT DE ZONE DE TRANSIT
     *
     * Ce constructeur crée un nouveau message pour informer de l'état
     * d'une zone de transit. Il appelle le constructeur de la classe
     * parente pour initialiser les informations de base du message.
     *
     * @param expediteur Le robot qui envoie l'information
     * @param coordonneeXTransit La coordonnée X de la zone de transit
     * @param coordonneeYTransit La coordonnée Y de la zone de transit
     * @param estPleine true si la zone est pleine, false si elle est disponible
     */
    public TransitZoneStatusMessage(MyTransitRobot expediteur, int coordonneeXTransit,
                                   int coordonneeYTransit, boolean estPleine) {
        // Appeler le constructeur de la classe parente
        super(expediteur, TYPE_MESSAGE);
        this.coordonneeXTransit = coordonneeXTransit;
        this.coordonneeYTransit = coordonneeYTransit;
        this.estPleine = estPleine;

        // Enregistrer l'envoi du message dans les logs
        LogManager.getInstance().logTransit(expediteur.getName(),
            "Zone de transit (" + coordonneeXTransit + "," + coordonneeYTransit + ")",
            "signalée comme " + (estPleine ? "pleine" : "disponible"));
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LA COORDONNÉE X DE LA ZONE DE TRANSIT
     *
     * Cette méthode retourne la coordonnée X de la zone de transit
     * dont on signale l'état. C'est utile pour identifier précisément
     * quelle zone est concernée.
     *
     * @return La coordonnée X de la zone de transit
     */
    public int getTransitX() {
        return coordonneeXTransit;
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LA COORDONNÉE Y DE LA ZONE DE TRANSIT
     *
     * Cette méthode retourne la coordonnée Y de la zone de transit
     * dont on signale l'état. Avec la coordonnée X, cela permet
     * d'identifier précisément la zone concernée.
     *
     * @return La coordonnée Y de la zone de transit
     */
    public int getTransitY() {
        return coordonneeYTransit;
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI LA ZONE DE TRANSIT EST PLEINE
     *
     * Cette méthode retourne l'état de la zone de transit.
     * Une zone pleine ne peut pas accueillir de nouveaux colis,
     * tandis qu'une zone disponible peut être utilisée.
     *
     * @return true si la zone est pleine, false si elle est disponible
     */
    public boolean isFull() {
        return estPleine;
    }

    /**
     * MÉTHODE POUR SÉRIALISER LE MESSAGE EN TEXTE
     *
     * Cette méthode convertit le message en une chaîne de caractères
     * qui peut être transmise sur le réseau entre les robots.
     *
     * Format : TYPE|transitX|transitY|isFull (1 ou 0)
     *
     * @return Le message sous forme de chaîne de caractères
     */
    @Override
    public String serialize() {
        return TYPE_MESSAGE + "|" + coordonneeXTransit + "|" + coordonneeYTransit + "|" +
               (estPleine ? "1" : "0");
    }

    /**
     * MÉTHODE STATIQUE POUR RECONSTRUIRE UN MESSAGE À PARTIR D'UN TEXTE
     *
     * Cette méthode fait l'inverse de serialize() : elle prend une chaîne
     * de caractères et recrée un TransitZoneStatusMessage complet. C'est utilisé
     * quand un robot reçoit un message par le réseau.
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de texte
     * @return Un nouveau TransitZoneStatusMessage, ou null si le texte est invalide
     */
    public static TransitZoneStatusMessage deserialize(MyTransitRobot expediteur, String texteSérialise) {
        // Diviser le texte en parties séparées par "|"
        String[] parties = texteSérialise.split("\\|");

        // Vérifier que le format est correct (4 parties attendues)
        if (parties.length < 4 || !parties[0].equals(TYPE_MESSAGE)) {
            return null; // Format invalide
        }

        // Extraire les informations du message
        int coordonneeXTransit = Integer.parseInt(parties[1]);
        int coordonneeYTransit = Integer.parseInt(parties[2]);
        boolean estPleine = parties[3].equals("1"); // "1" = pleine, "0" = disponible

        // Recréer le message
        return new TransitZoneStatusMessage(expediteur, coordonneeXTransit, coordonneeYTransit, estPleine);
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     *
     * Cette méthode est appelée automatiquement quand un robot reçoit
     * ce message. Elle transmet l'information d'état au coordinateur
     * pour qu'il mette à jour sa connaissance des zones de transit.
     *
     * @param destinataire Le robot qui reçoit le message
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Transmettre l'information d'état au coordinateur
        destinataire.getCoordinateurTaches().handleTransitZoneStatus(coordonneeXTransit, coordonneeYTransit, estPleine);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logTransit(destinataire.getName(),
            "Zone de transit (" + coordonneeXTransit + "," + coordonneeYTransit + ")",
            "reçue comme " + (estPleine ? "pleine" : "disponible"));
    }
}

/**
 * FABRIQUE DE MESSAGES POUR LA CRÉATION D'OBJETS MESSAGE À PARTIR DE TEXTE
 *
 * Cette classe utilitaire implémente le pattern Factory pour créer des objets
 * Message à partir de chaînes de caractères sérialisées. Elle analyse le type
 * de message et appelle la méthode de désérialisation appropriée.
 *
 * Rôle dans le système :
 * 1. Recevoir un message sous forme de texte depuis le réseau
 * 2. Identifier le type de message grâce au préfixe
 * 3. Appeler la méthode deserialize() de la classe appropriée
 * 4. Retourner un objet Message complet et utilisable
 * 5. Gérer les erreurs de format et les types inconnus
 *
 * Cette classe centralise la logique de création des messages et simplifie
 * le processus de réception des messages dans le système décentralisé.
 */
class MessageFactory {

    /**
     * MÉTHODE STATIQUE POUR CRÉER UN OBJET MESSAGE À PARTIR D'UN TEXTE SÉRIALISÉ
     *
     * Cette méthode est le point d'entrée principal de la fabrique. Elle analyse
     * le texte reçu, identifie le type de message, et crée l'objet approprié.
     * C'est une méthode statique car elle ne nécessite pas d'instance de la classe.
     *
     * Processus de création :
     * 1. Vérifier que le texte n'est pas vide ou null
     * 2. Extraire le type de message (première partie avant "|")
     * 3. Utiliser un switch pour appeler la bonne méthode deserialize()
     * 4. Retourner l'objet Message créé ou null en cas d'erreur
     *
     * @param expediteur Le robot qui a envoyé le message original
     * @param texteSérialise Le message sous forme de chaîne de caractères
     * @return Un objet Message du bon type, ou null si impossible à créer
     */
    public static Message createMessage(MyTransitRobot expediteur, String texteSérialise) {
        // Étape 1 : Vérifier que le texte n'est pas vide
        if (texteSérialise == null || texteSérialise.isEmpty()) {
            return null; // Impossible de créer un message à partir d'un texte vide
        }

        // Étape 2 : Diviser le texte pour extraire le type de message
        String[] parties = texteSérialise.split("\\|");
        if (parties.length < 1) {
            return null; // Format invalide, pas de type de message
        }

        // Étape 3 : Identifier le type de message
        String typeMessage = parties[0];

        // Étape 4 : Créer l'objet approprié selon le type
        switch (typeMessage) {
            case "NEW_TASK":
                // Message d'annonce d'une nouvelle tâche
                return NewTaskMessage.deserialize(expediteur, texteSérialise);

            case "OFFRE":
                // Message d'offre (enchère) pour une tâche
                return MessageOffre.deserialize(expediteur, texteSérialise);

            case "BID_ACCEPTED":
                // Message de notification d'acceptation d'offre
                return BidAcceptedMessage.deserialize(expediteur, texteSérialise);

            case "BID_REJECTED":
                // Message de notification de rejet d'offre
                return BidRejectedMessage.deserialize(expediteur, texteSérialise);

            case "TASK_ASSIGNED":
                // Message de notification d'assignation de tâche
                return TaskAssignedMessage.deserialize(expediteur, texteSérialise);

            case "TASK_COMPLETED":
                // Message de notification de tâche terminée
                return TaskCompletedMessage.deserialize(expediteur, texteSérialise);

            case "EFFICIENCY_UPDATE":
                // Message de mise à jour d'efficacité
                return EfficiencyUpdateMessage.deserialize(expediteur, texteSérialise);

            case "TRANSIT_ZONE_STATUS":
                // Message d'état de zone de transit
                return TransitZoneStatusMessage.deserialize(expediteur, texteSérialise);

            default:
                // Type de message inconnu ou non supporté
                LogManager.getInstance().logError("MessageFactory",
                    "Type de message inconnu : " + typeMessage);
                return null;
        }
    }
}
