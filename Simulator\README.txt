DOCUMENTATION EXHAUSTIVE - SYSTÈME DE SIMULATION ROBOTIQUE DÉCENTRALISÉ
NOA AKAYAD
Mines Saint-Étienne
Décembre 2024

================================================================================
           GUIDE COMPLET POUR COMPRENDRE LE SYSTÈME D'ENCHÈRES ROBOTIQUE
================================================================================

INTRODUCTION - POURQUOI CE DOCUMENT ?

Ce document a été conçu pour permettre à toute personne, même sans connaissance préalable
en robotique ou en programmation, de comprendre intégralement le fonctionnement de notre
système de simulation d'entrepôt automatisé. Après avoir lu cette documentation, vous
devriez être capable d'expliquer précisément comment les robots prennent leurs décisions,
comment ils communiquent entre eux, et pourquoi ce système est plus efficace que les
approches traditionnelles.

J'ai structuré cette explication en partant des concepts les plus généraux pour aller
progressivement vers les détails techniques les plus fins. Chaque terme technique est
expliqué lors de sa première utilisation, et chaque processus est décomposé étape par étape
avec des exemples concrets.

================================================================================
                            PARTIE 1 - COMPRENDRE LE PROBLÈME
================================================================================

1. LE DÉFI DE LA COORDINATION ROBOTIQUE

Imaginez un entrepôt Amazon avec des dizaines de robots qui doivent livrer des colis.
Le problème fondamental est : comment ces robots décident-ils qui fait quoi ? Comment
évitent-ils de tous se précipiter sur le même colis ? Comment s'assurent-ils qu'aucun
colis n'est oublié ?

Traditionnellement, on résout ce problème avec un "contrôleur central" - un ordinateur
qui joue le rôle du chef d'orchestre. Ce chef regarde tous les colis à livrer, regarde
où sont tous les robots, et dit à chacun : "Toi, va chercher le colis A", "Toi, va
chercher le colis B", etc.

Cette approche fonctionne, mais elle a des défauts majeurs :
- Si le contrôleur central tombe en panne, tous les robots s'arrêtent
- Plus on ajoute de robots, plus le contrôleur devient surchargé
- Le contrôleur doit connaître en permanence l'état exact de tous les robots
- Les décisions sont lentes car tout passe par un seul point

2. NOTRE SOLUTION : LES ENCHÈRES DÉCENTRALISÉES

Notre approche révolutionnaire élimine complètement le contrôleur central. À la place,
nous utilisons un système d'enchères où chaque robot prend ses propres décisions.

Voici comment ça marche en pratique :

Étape 1 - Découverte d'une tâche
Quand un nouveau colis apparaît dans l'entrepôt, le premier robot qui le voit annonce
à tous les autres : "Attention ! Il y a un nouveau colis à livrer à la position [15,23],
destination [45,67]".

Étape 2 - Évaluation individuelle
Chaque robot qui entend cette annonce réfléchit : "Est-ce que cette tâche m'intéresse ?
Suis-je bien placé pour la faire ? Ai-je assez de batterie ?" Chaque robot calcule son
propre "score d'intérêt" pour cette tâche.

Étape 3 - Enchères
Les robots intéressés font des "offres" (comme dans une vente aux enchères). Une offre
contient essentiellement : "Moi, Robot5, je peux faire cette tâche avec un score de 0.85".

Étape 4 - Attribution automatique
Après un court délai, le robot qui a fait la meilleure offre (le score le plus élevé)
remporte automatiquement la tâche et commence à l'exécuter.

Cette approche est révolutionnaire car :
- Aucun point de défaillance unique
- Chaque robot optimise ses propres décisions
- Le système s'adapte automatiquement au nombre de robots
- Les décisions sont prises en parallèle, donc plus rapidement

3. LES AVANTAGES CONCRETS

Pour bien comprendre pourquoi cette approche est supérieure, prenons un exemple concret.

Scénario traditionnel (avec contrôleur central) :
- 3 nouveaux colis apparaissent simultanément
- Le contrôleur doit analyser la position de tous les robots
- Il calcule la meilleure attribution pour les 3 colis
- Il envoie les ordres à chaque robot
- Si un robot tombe en panne, le contrôleur doit recalculer tout

Scénario avec notre système d'enchères :
- 3 nouveaux colis apparaissent simultanément
- Chaque colis déclenche automatiquement ses propres enchères
- Les robots évaluent en parallèle leur intérêt pour chaque colis
- Les 3 attributions se font simultanément et automatiquement
- Si un robot tombe en panne, les autres continuent normalement

Le gain en efficacité et en robustesse est considérable !

================================================================================
                        PARTIE 2 - ARCHITECTURE DU SYSTÈME
================================================================================

4. VUE D'ENSEMBLE DE L'ARCHITECTURE

Avant de plonger dans les détails techniques, il est essentiel de comprendre comment
tous les composants s'articulent ensemble. Notre système ressemble à une ville miniature
avec ses habitants (les robots), ses infrastructures (zones et stations), et son système
de communication (les messages).

Voici les acteurs principaux et leur rôle :

LES ROBOTS (MyTransitRobot) - Les Habitants Autonomes
Chaque robot est comme un livreur indépendant qui :
- Observe son environnement pour détecter les opportunités
- Prend ses propres décisions sur les tâches à accomplir
- Communique avec ses collègues pour coordonner les efforts
- Gère sa propre énergie (batterie) de manière optimale

L'INFRASTRUCTURE (StaticComponents) - Les Bâtiments Fixes
L'entrepôt contient des zones spécialisées qui ne bougent jamais :
- Zones de départ : où apparaissent les nouveaux colis
- Zones de transit : points de relais pour optimiser les trajets
- Zones de destination : où les colis doivent être livrés
- Stations de charge : où les robots rechargent leur batterie

LE SYSTÈME NERVEUX (Messages) - Le Réseau de Communication
Les robots communiquent via des messages spécialisés :
- Annonces de nouvelles tâches
- Offres d'enchères pour obtenir des tâches
- Confirmations de livraison
- Partage d'informations sur les performances

LE SYSTÈME DE SURVEILLANCE (LogManager) - L'Observateur
Un système qui enregistre tout ce qui se passe :
- Actions de chaque robot avec horodatage
- Communications entre robots
- Événements de batterie et de charge
- Erreurs et situations exceptionnelles

5. COMMENT LES COMPOSANTS INTERAGISSENT

Pour bien comprendre le système, imaginons un scénario complet du début à la fin.

SCÉNARIO : Livraison d'un colis de [10,15] vers [45,67]

Étape 1 : Apparition du colis
Un nouveau colis apparaît dans la zone de départ située en [10,15]. Cette zone est
un composant statique qui ne peut pas bouger.

Étape 2 : Découverte par un robot
Robot3, qui patrouille dans le secteur, détecte le nouveau colis lors de son passage
près de la zone de départ. Son système de perception (updatePerception) lui signale
la présence du colis.

Étape 3 : Création et annonce de la tâche
Robot3 utilise son CoordinateurTaches pour créer une nouvelle tâche via la méthode
`createTask(ColorPackage colis, ColorStartZone zoneDepart, Map<Integer, int[]> destinations)` :
- Identifiant : "Task-Colis47"
- Position de départ : [10,15]
- Destination : [45,67]

Robot3 diffuse alors un NewTaskMessage à tous les autres robots via
`broadcastMessage(NewTaskMessage message)` : "Nouvelle tâche disponible :
Task-Colis47, départ [10,15], destination [45,67]".

Étape 4 : Évaluation par tous les robots
Chaque robot (Robot1, Robot2, Robot4, Robot5, etc.) reçoit l'annonce et évalue
son intérêt pour cette tâche :

Robot1 (position [5,20], batterie 85%) :
- Distance jusqu'au départ : 7.07 cases
- Distance totale estimée : 52.07 cases
- Score d'utilité : 0.73

Robot2 (position [25,30], batterie 45%) :
- Distance jusqu'au départ : 25.0 cases
- Batterie faible, score pénalisé
- Score d'utilité : 0.31

Robot4 (position [8,12], batterie 95%) :
- Distance jusqu'au départ : 3.61 cases
- Excellente position et batterie
- Score d'utilité : 0.91

Étape 5 : Phase d'enchères
Les robots intéressés (ceux avec un score > 0.5) envoient leurs offres :
- Robot1 envoie : BidMessage("Task-Colis47", "Robot1", 0.73)
- Robot4 envoie : BidMessage("Task-Colis47", "Robot4", 0.91)

Étape 6 : Attribution de la tâche
Après 2 secondes de délai d'enchères, Robot4 remporte la tâche avec le meilleur score.
Tous les robots mettent à jour leur base de connaissances : "Task-Colis47 = Robot4".

Étape 7 : Exécution de la tâche
Robot4 se dirige vers [10,15], prend le colis, et commence son trajet vers [45,67].
Il peut choisir entre un trajet direct ou passer par une zone de transit selon
l'efficacité calculée.

Étape 8 : Livraison et notification
Une fois arrivé en [45,67], Robot4 livre le colis et diffuse un TaskCompletionMessage
pour informer tous les robots que la tâche est terminée.

Ce scénario illustre parfaitement comment tous les composants travaillent ensemble
de manière coordonnée mais décentralisée.

================================================================================
                    PARTIE 3 - MÉCANISMES INTERNES DÉTAILLÉS
================================================================================

6. LE CERVEAU DU ROBOT : CoordinateurTaches

Chaque robot possède son propre "cerveau" sous la forme d'une instance de la classe
CoordinateurTaches. C'est ici que se prennent toutes les décisions importantes.
Comprendre ce composant est crucial car c'est lui qui rend le système décentralisé
possible.

6.1 Structure de la mémoire du coordinateur

Le coordinateur maintient plusieurs "bases de données" en mémoire :

Base des tâches connues (Map<String, List<Task>> tachesConnues)
Cette base stocke toutes les tâches que le robot a découvertes, organisées par zone.
Exemple de contenu :
- "Zone_[10,15]" → [Task-Colis47, Task-Colis52, Task-Colis61]
- "Zone_[25,30]" → [Task-Colis48, Task-Colis55]

Base des offres émises (Map<String, Enchere> mesOffres)
Le robot garde trace de toutes les offres qu'il a faites pour éviter les doublons.
Exemple : "Task-Colis47" → Enchere(Robot4, 0.91, timestamp)

Base des offres reçues (Map<String, List<Enchere>> offresRecues)
Stocke toutes les offres des autres robots pour chaque tâche.
Exemple : "Task-Colis47" → [Enchere(Robot1, 0.73), Enchere(Robot4, 0.91)]

Base des attributions (Map<String, String> attributionsTaches)
Garde trace de quel robot a été assigné à quelle tâche.
Exemple : "Task-Colis47" → "Robot4"

Base des performances (Map<String, Double> scoresEfficaciteRobots)
Stocke les scores d'efficacité de chaque robot pour améliorer les décisions futures.
Exemple : "Robot1" → 0.85, "Robot4" → 0.92

6.2 L'algorithme de calcul d'utilité

Quand un robot évalue son intérêt pour une tâche, il utilise une formule sophistiquée
qui prend en compte 6 facteurs différents. Ce calcul est implémenté dans la méthode
`calculateUtility(MyTransitRobot robot, Task tache)` du fichier `CoordinateurTaches.java`.

Voici l'implémentation concrète de cette méthode cruciale :

```java
/**
 * MÉTHODE POUR CALCULER L'UTILITÉ D'UNE TÂCHE POUR UN ROBOT
 * Cette méthode est le cœur de l'algorithme de décision.
 */
private double calculateUtility(MyTransitRobot robot, Task tache) {
    // Facteur 1 : Distance vers la tâche (facteur le plus critique)
    double distanceVersTache = calculateDistance(robot.getX(), robot.getY(),
                                               tache.getStartX(), tache.getStartY());

    // Utiliser une fonction exponentielle décroissante pour favoriser la proximité
    double utiliteDistance = Math.exp(-0.2 * distanceVersTache);

    // Facteur 2 : Niveau de batterie (considération minimale mais importante)
    double niveauBatterie = robot.getBatteryLevel() / 100.0;
    double utiliteBatterie = (niveauBatterie > 0.2) ? 1.0 : 0.0;

    // Facteur 3 : Score d'efficacité historique
    String robotId = robot.getName();
    double scoreEfficacite = scoresEfficaciteRobots.getOrDefault(robotId, 1.0);

    // Facteur 4 : Équilibrage de charge
    int nombreTachesActuelles = getNombreTachesAssignees(robotId);
    double bonusEquilibrage = 0.1 / (1.0 + nombreTachesActuelles);

    // Facteur 5 : Distance vers la destination finale
    double distanceDestination = calculateDistance(tache.getStartX(), tache.getStartY(),
                                                  tache.getGoalX(), tache.getGoalY());
    double utiliteDestination = Math.exp(-0.05 * distanceDestination);

    // Combiner les utilités de distance
    utiliteDistance = (utiliteDistance + utiliteDestination) / 2.0;

    // Calcul final avec tous les facteurs pondérés
    return (POIDS_DISTANCE * utiliteDistance) +
           (POIDS_BATTERIE * utiliteBatterie) +
           (POIDS_EFFICACITE * scoreEfficacite) +
           bonusEquilibrage;
}
```

ANALYSE DÉTAILLÉE DES FACTEURS :

FACTEUR 1 : Distance jusqu'à la tâche
Formule : utiliteDistance = Math.exp(-0.2 * distance)
Plus le robot est proche, plus son utilité est élevée.
Exemple : distance = 5 cases → utilité = 0.37
         distance = 10 cases → utilité = 0.14

FACTEUR 2 : Niveau de batterie
Formule : utiliteBatterie = (niveauBatterie > 0.2) ? 1.0 : 0.0
Décision binaire : soit le robot a assez de batterie, soit il n'en a pas.
Exemple : batterie = 90% → utilité = 1.0
         batterie = 15% → utilité = 0.0

FACTEUR 3 : Historique de performance
Formule : scoreEfficacite = scoresEfficaciteRobots.get(robotId)
Les robots qui ont été efficaces dans le passé sont favorisés.
Exemple : robot très efficace → score = 0.95
         robot moins efficace → score = 0.65

FACTEUR 4 : Équilibrage de charge
Formule : bonusEquilibrage = 0.1 / (1.0 + nombreTachesActuelles)
Les robots moins chargés reçoivent un bonus.
Exemple : 0 tâche → bonus = 0.10
         2 tâches → bonus = 0.033

FACTEUR 5 : Distance vers la destination finale
Formule : utiliteDestination = Math.exp(-0.05 * distanceDestination)
Favorise les tâches avec des destinations plus proches.

CALCUL FINAL :
utiliteFinale = (0.7 * utiliteDistance) + (0.1 * utiliteBatterie) +
                (0.2 * scoreEfficacite) + bonusEquilibrage

Les coefficients (0.7, 0.1, 0.2) définissent l'importance relative de chaque facteur.

6.3 Exemple concret de calcul d'utilité

Prenons Robot4 qui évalue Task-Colis47 :
- Position robot : [8,12]
- Position tâche : [10,15]
- Destination : [45,67]
- Batterie : 95%
- Score efficacité historique : 0.92
- Tâches actuelles : 0

Calcul étape par étape :

1. Distance jusqu'à la tâche : sqrt((10-8)² + (15-12)²) = 3.61
   utiliteDistance = exp(-0.1 * 3.61) = 0.697

2. Niveau de batterie : 95%
   utiliteBatterie = 95/100 = 0.95

3. Score d'efficacité : 0.92 (historique)

4. Bonus équilibrage : 0.1 / (1 + 0) = 0.1

5. Bonus priorité : 3 * 0.05 = 0.15

6. Distance destination : sqrt((45-10)² + (67-15)²) = 62.04
   utiliteDestination = exp(-0.05 * 62.04) = 0.049

7. Moyenne des utilités de distance : (0.697 + 0.049) / 2 = 0.373

8. Calcul final :
   utiliteFinale = (0.4 * 0.373) + (0.3 * 0.95) + (0.2 * 0.92) + 0.1 + 0.15
                 = 0.149 + 0.285 + 0.184 + 0.1 + 0.15
                 = 0.868

Robot4 obtient donc un score d'utilité de 0.868 pour cette tâche, ce qui est excellent.

Une fois l'utilité calculée, si elle dépasse le seuil minimum, le robot génère une
offre via la méthode `genererOffre(Task tache)` dans `CoordinateurTaches.java`, puis
la diffuse aux autres robots.

Voici l'implémentation de la génération d'offres :

```java
/**
 * MÉTHODE POUR GÉNÉRER UNE OFFRE D'ENCHÈRE POUR UNE TÂCHE
 * Cette méthode évalue si le robot doit faire une offre et la crée si nécessaire.
 */
private Enchere genererOffre(Task tache) {
    // Étape 1 : Calculer l'utilité de cette tâche pour notre robot
    double utilite = calculateUtility(robotProprietaire, tache);

    LogManager.getInstance().logCoordination(robotProprietaire.getName(),
        "Utilité calculée pour tâche " + tache.getId() + ": " +
        String.format("%.2f", utilite));

    // Étape 2 : Vérifier que l'utilité est positive
    if (utilite <= 0) {
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Pas d'offre pour tâche " + tache.getId() + " - utilité trop faible");
        return null;
    }

    // Étape 3 : Créer l'offre avec l'utilité calculée
    Enchere nouvelleOffre = new Enchere(tache.getId(), robotProprietaire.getName(),
                                       utilite, System.currentTimeMillis());

    // Étape 4 : Enregistrer l'offre dans notre base de données
    mesOffres.put(tache.getId(), nouvelleOffre);

    return nouvelleOffre;
}
```

Et voici comment une tâche est créée quand un robot découvre un nouveau colis :

```java
/**
 * MÉTHODE POUR CRÉER UNE NOUVELLE TÂCHE QUAND UN COLIS EST DÉCOUVERT
 * Cette méthode transforme un colis en tâche et l'annonce à tous les robots.
 */
public Task createTask(ColorPackage colis, ColorStartZone zoneDepart,
                      Map<Integer, int[]> destinations) {
    // Étape 1 : Générer un identifiant unique pour la tâche
    String identifiantTache = "Task-Colis" + System.currentTimeMillis();

    // Étape 2 : Déterminer les coordonnées de départ et de destination
    int xDepart = zoneDepart.getX();
    int yDepart = zoneDepart.getY();
    int[] coordonneesDestination = destinations.get(colis.getDestinationGoalId());

    // Étape 3 : Créer l'objet Task
    Task nouvelleTache = new Task(identifiantTache, xDepart, yDepart,
                                 coordonneesDestination[0], coordonneesDestination[1],
                                 colis);

    // Étape 5 : Enregistrer la tâche dans notre base de connaissances
    String identifiantZone = "[" + xDepart + "," + yDepart + "]";
    tachesConnues.computeIfAbsent(identifiantZone, k -> new ArrayList<>());
    tachesConnues.get(identifiantZone).add(nouvelleTache);

    // Étape 6 : Diffuser l'annonce de la nouvelle tâche à tous les robots
    NewTaskMessage messageNouvelleTache = new NewTaskMessage(robotProprietaire, nouvelleTache);
    robotProprietaire.broadcastMessage(messageNouvelleTache);

    return nouvelleTache;
}
```

7. LES ÉTATS DU ROBOT ET LEURS TRANSITIONS

Chaque robot fonctionne comme une machine à états finis implémentée dans l'énumération
`TransitState` du fichier `MyTransitRobot.java`. La logique de transition entre états
est gérée par la méthode principale `step()` de cette même classe. Comprendre ces états
et leurs transitions est essentiel pour saisir le comportement des robots.

7.1 Les cinq états principaux

FREE (LIBRE) - État d'attente et de recherche
Le robot n'a pas de tâche assignée et cherche activement des opportunités.
Comportement :
- Patrouille dans l'entrepôt pour détecter de nouveaux colis
- Écoute les annonces de nouvelles tâches des autres robots
- Évalue et fait des offres via `taskCoordinator.findBestTaskForRobot()`
- Se dirige vers la zone d'attente centrale si aucune tâche n'est disponible

GOING_TO_TRANSIT - En route vers une zone de transit
Le robot a décidé d'utiliser une zone de transit pour optimiser sa livraison.
Comportement :
- Se déplace vers la zone de transit choisie
- Évite les obstacles et autres robots
- Surveille son niveau de batterie
- Peut abandonner et aller charger si la batterie devient critique

AT_TRANSIT - À la zone de transit
Le robot est arrivé à la zone de transit et gère le transfert du colis.
Comportement :
- Dépose le colis dans la zone de transit
- Met à jour les informations sur l'occupation de la zone
- Informe les autres robots de la disponibilité du colis
- Passe à l'état FREE pour chercher une nouvelle tâche

GOING_TO_GOAL - En route vers la destination finale
Le robot transporte un colis directement vers sa destination finale.
Comportement :
- Se déplace vers les coordonnées de destination
- Optimise son trajet en évitant les obstacles
- Surveille sa batterie et peut aller charger si nécessaire
- Peut passer par une zone de transit si c'est plus efficace

RETURNING_TO_CENTER - Retour à la zone d'attente
Le robot a terminé une livraison et retourne à la zone d'attente centrale.
Comportement :
- Se dirige vers le centre de l'entrepôt
- Évite les zones congestionnées
- Passe à l'état FREE une fois arrivé au centre

7.2 Diagramme de transition d'états

Voici comment les robots passent d'un état à l'autre :

    [FREE] ──(colis détecté)──> [GOING_TO_TRANSIT] ──(arrivé)──> [AT_TRANSIT]
       ↑                              │                              │
       │                              │                              │
       │                         (batterie faible)              (colis déposé)
       │                              │                              │
       │                              ↓                              ↓
    [RETURNING_TO_CENTER] <──(livraison terminée)──< [GOING_TO_GOAL] <──┘
       │                                                    ↑
       │                                                    │
       └──(arrivé au centre)──> [FREE] ──(trajet direct)───┘

7.3 Gestion intelligente de la batterie

La gestion de la batterie est cruciale pour l'efficacité du système. Chaque robot
surveille constamment son niveau d'énergie et prend des décisions intelligentes.
Cette logique est implémentée dans plusieurs méthodes de `MyTransitRobot.java` :
`consumeBatteryForMovement()`, `consumeBatteryForPickup()`, `chargeBattery()`, et
`findNearestChargingStation()`.

Voici les implémentations concrètes de ces méthodes critiques :

```java
/**
 * MÉTHODE POUR CONSOMMER DE LA BATTERIE LORS D'UN DÉPLACEMENT
 * Cette méthode gère la consommation d'énergie à chaque mouvement du robot.
 */
public void consumeBatteryForMovement() {
    // Étape 1 : Sauvegarder le niveau de batterie avant consommation
    double niveauAvantConsommation = batteryLevel;

    // Étape 2 : Déduire le coût énergétique du mouvement
    batteryLevel -= MOVE_BATTERY_COST;

    // Étape 3 : Enregistrer la consommation détaillée dans les logs
    LogManager.getInstance().logBattery(getName(), batteryLevel,
        String.format("Mouvement: -%.1f%% (%.1f%% → %.1f%%)",
                     MOVE_BATTERY_COST, niveauAvantConsommation, batteryLevel));

    // Étape 4 : Vérifier si la batterie est devenue faible
    if (batteryLevel < LOW_BATTERY_THRESHOLD && !isCharging) {
        LogManager.getInstance().logBattery(getName(), batteryLevel,
                                          "niveau de batterie faible");
        broadcastLowBatteryMessage();
    }
}

/**
 * MÉTHODE POUR CHARGER LA BATTERIE DU ROBOT
 * Cette méthode gère le processus de recharge avec optimisation du temps.
 */
private void chargeBattery() {
    if (isCharging) {
        // Étape 1 : Sauvegarder le niveau avant recharge
        double niveauAvantRecharge = batteryLevel;

        // Étape 2 : Augmenter le niveau selon le taux de recharge
        batteryLevel += CHARGING_RATE;

        // Étape 3 : Vérifier si la batterie a atteint le maximum
        if (batteryLevel >= MAX_BATTERY) {
            batteryLevel = MAX_BATTERY;
            isCharging = false;
            LogManager.getInstance().logCharging(getName(), niveauAvantRecharge,
                                               batteryLevel, "a terminé de charger");
        } else {
            LogManager.getInstance().logCharging(getName(), niveauAvantRecharge,
                                               batteryLevel, "est en train de charger");
        }

        // Étape 4 : Mettre à jour la couleur du robot
        updateRobotColor();
    }
}
```

PARAMÈTRES DE BATTERIE :
- MAX_BATTERY = 100% (capacité maximale)
- CRITICAL_BATTERY_THRESHOLD = 20% (seuil critique)
- LOW_BATTERY_THRESHOLD = 30% (seuil d'alerte)
- MOVE_BATTERY_COST = 1% (coût d'un déplacement)
- PICKUP_BATTERY_COST = 2% (coût pour prendre un colis)
- CHARGE_RATE = 5% par étape (vitesse de charge)

STRATÉGIE DE CHARGE OPTIMISÉE :
Contrairement à ce qu'on pourrait penser, les robots ne chargent PAS dès que possible.
Ils utilisent une stratégie "charge critique uniquement" pour maximiser leur temps
de travail.

Voici l'implémentation de cette stratégie dans la méthode step() :

```java
// OPTIMISATION EXTRÊME : Gestion ultra-agressive de la batterie
if (isCharging) {
    // Si déjà en charge, charger seulement jusqu'à 50% pour gagner du temps
    if (batteryLevel < MAX_BATTERY * 0.5) {
        chargeBattery();
        return;
    } else {
        // Arrêter la charge une fois qu'on a assez de batterie
        isCharging = false;
    }
}

// Charger seulement si critique - jamais de charge opportuniste
if (batteryLevel <= CRITICAL_BATTERY_THRESHOLD) {
    if (isNearChargingStation()) {
        isCharging = true;
        LogManager.getInstance().logBattery(getName(), batteryLevel,
                                          "a commencé à charger sa batterie");
        chargeBattery();
        return;
    } else {
        // Se déplacer vers une station de charge seulement si critique
        int[] nearestCS = findNearestChargingStation();
        if (nearestCS != null) {
            moveOneStepTo(nearestCS[0], nearestCS[1]);
            consumeBatteryForMovement();
            return;
        }
    }
}
```

Algorithme de décision de charge :

1. Si batterie > 30% : Continuer les tâches normalement
2. Si 20% < batterie ≤ 30% : Alerte, mais continuer si tâche en cours
3. Si batterie ≤ 20% : ARRÊT IMMÉDIAT, aller charger obligatoirement

Cette stratégie évite les "charges opportunistes" qui feraient perdre du temps.

7.4 Exemple de cycle de vie complet d'un robot

Suivons Robot2 pendant une journée complète :

ÉTAPE 1 (t=0) : Initialisation
- État : FREE
- Position : [25,25] (centre)
- Batterie : 100%
- Action : Commence à patrouiller

ÉTAPE 2 (t=15) : Découverte d'une tâche
- Détecte un colis en [10,15]
- Calcule utilité : 0.76
- Fait une offre et remporte la tâche
- État : GOING_TO_TRANSIT (décide d'utiliser zone de transit)

ÉTAPE 3 (t=28) : Arrivée à la zone de transit
- Position : [20,20] (zone de transit)
- Batterie : 87% (13% consommés pour le trajet)
- Prend le colis et le dépose dans la zone
- État : AT_TRANSIT

ÉTAPE 4 (t=30) : Recherche de nouvelle tâche
- Colis déposé avec succès
- État : FREE
- Commence à chercher une nouvelle opportunité

ÉTAPE 5 (t=45) : Nouvelle tâche - trajet direct
- Remporte une tâche pour livraison directe
- Destination : [35,40]
- État : GOING_TO_GOAL

ÉTAPE 6 (t=62) : Livraison réussie
- Colis livré à destination
- Batterie : 68%
- État : RETURNING_TO_CENTER

ÉTAPE 7 (t=75) : Retour au centre
- Position : [25,25]
- État : FREE
- Prêt pour de nouvelles tâches

Ce cycle se répète jusqu'à ce que la batterie atteigne 20%, moment où le robot
interrompt tout pour aller charger.

8. ALGORITHMES DE NAVIGATION ET PATHFINDING

La navigation des robots est un aspect crucial qui détermine l'efficacité globale
du système. Chaque robot doit pouvoir se déplacer intelligemment dans l'entrepôt
en évitant les obstacles et en optimisant ses trajets.

8.1 L'algorithme de déplacement pas à pas

Les robots utilisent un algorithme de pathfinding glouton (greedy) qui choisit
à chaque étape la meilleure direction vers la destination. Cet algorithme est
implémenté dans la méthode `moveOneStepTo(int targetX, int targetY)` du fichier
`MyRobot.java`.

Voici l'implémentation complète de cet algorithme crucial :

```java
/**
 * MÉTHODE POUR FAIRE AVANCER LE ROBOT UN PAS VERS UNE DESTINATION
 * Cette méthode implémente un algorithme de pathfinding glouton qui choisit
 * la meilleure direction à chaque étape.
 */
protected void moveOneStepTo(int targetX, int targetY) {
    // Étape 1 : Obtenir toutes les directions possibles
    HashMap<String, Location> directions = getNextCoordinate();
    Location bestMove = null;
    double minDist = Double.MAX_VALUE;

    // Étape 2 : Évaluer chaque direction possible
    for (Map.Entry<String, Location> entry : directions.entrySet()) {
        Location loc = entry.getValue();

        // Étape 3 : Vérifier les limites de la grille
        if (loc.getX() < 0 || loc.getX() >= rows ||
            loc.getY() < 0 || loc.getY() >= columns) continue;

        // Étape 4 : Vérifier si la case est libre
        if (!isCellFree(loc.getX(), loc.getY())) continue;

        // Étape 5 : Calculer la distance vers la destination
        double dist = distanceTo(loc.getX(), loc.getY(), targetX, targetY);

        // Étape 6 : Garder la meilleure direction
        if (dist < minDist) {
            minDist = dist;
            bestMove = loc;
        }
    }

    // Étape 7 : Exécuter le mouvement vers la meilleure position
    if (bestMove != null) {
        // Orienter le robot dans la bonne direction
        if (bestMove.getX() == this.getX() - 1) setCurrentOrientation(Orientation.up);
        if (bestMove.getX() == this.getX() + 1) setCurrentOrientation(Orientation.down);
        if (bestMove.getY() == this.getY() - 1) setCurrentOrientation(Orientation.left);
        if (bestMove.getY() == this.getY() + 1) setCurrentOrientation(Orientation.right);

        // Effectuer le mouvement
        moveForward();

        // Consommer la batterie si c'est un MyTransitRobot
        if (this instanceof MyTransitRobot) {
            ((MyTransitRobot)this).consumeBatteryForMovement();
        }
    }
}
```

ANALYSE DÉTAILLÉE DE L'ALGORITHME :

1. GÉNÉRATION DES DIRECTIONS POSSIBLES
   Le robot examine les 4 directions cardinales (haut, bas, gauche, droite) :
   - Nord : [x-1, y]
   - Sud : [x+1, y]
   - Ouest : [x, y-1]
   - Est : [x, y+1]

2. FILTRAGE DES DIRECTIONS VALIDES
   Pour chaque direction, vérifier :
   - La case est-elle dans les limites de la grille ?
   - La case est-elle libre (pas d'obstacle, pas d'autre robot) ?
   - La case n'est-elle pas une zone interdite ?

3. CALCUL DE LA DISTANCE POUR CHAQUE DIRECTION VALIDE
   Pour chaque direction possible, calculer :
   distance = sqrt((targetX - newX)² + (targetY - newY)²)

4. SÉLECTION DE LA MEILLEURE DIRECTION
   Choisir la direction qui minimise la distance vers la cible.

5. EXÉCUTION DU MOUVEMENT
   - Orienter le robot dans la direction choisie
   - Exécuter moveForward()
   - Consommer la batterie (1% par mouvement)

8.2 Gestion des collisions et blocages

Quand un robot ne peut pas avancer (toutes les directions sont bloquées), il
utilise une stratégie d'attente intelligente :

Algorithme de gestion des blocages :

1. DÉTECTION DU BLOCAGE
   Si aucune direction n'est libre vers la destination, le robot est bloqué.

2. STRATÉGIE D'ATTENTE ADAPTATIVE
   - Attendre 1-3 étapes pour voir si la situation se débloque
   - Si le blocage persiste, essayer une direction alternative
   - En dernier recours, faire un petit détour pour contourner l'obstacle

3. ÉVITEMENT PROACTIF
   Les robots anticipent les collisions en observant les mouvements des autres
   robots et en ajustant leur trajectoire en conséquence.

8.3 Optimisation des trajets : Direct vs Transit

Une innovation majeure de notre système est la capacité des robots à choisir
intelligemment entre un trajet direct et un passage par une zone de transit.
Cette décision est prise par la méthode `shouldUseTransitZone(int destX, int destY)`
dans `MyTransitRobot.java`.

Voici l'implémentation de cet algorithme de décision intelligent :

```java
/**
 * MÉTHODE POUR DÉCIDER SI UTILISER UNE ZONE DE TRANSIT
 * Cette méthode compare l'efficacité d'un trajet direct versus un passage
 * par une zone de transit pour optimiser la livraison.
 */
private boolean shouldUseTransitZone(int destX, int destY) {
    // Étape 1 : Calculer la distance directe vers la destination
    double distanceDirecte = calculateDistance(getX(), getY(), destX, destY);

    // Étape 2 : Trouver la zone de transit la plus efficace
    ColorTransitZone meilleureZoneTransit = null;
    double meilleurDistanceTotale = Double.MAX_VALUE;

    for (ColorTransitZone tz : transitZones) {
        if (!tz.isFull()) {  // Seulement les zones non pleines
            // Calculer distance vers la zone de transit
            double distanceVersTransit = calculateDistance(getX(), getY(),
                                                          tz.getX(), tz.getY());
            // Calculer distance de la zone vers la destination
            double distanceTransitVersDestination = calculateDistance(tz.getX(), tz.getY(),
                                                                     destX, destY);

            double distanceTotaleViaTransit = distanceVersTransit + distanceTransitVersDestination;

            if (distanceTotaleViaTransit < meilleurDistanceTotale) {
                meilleurDistanceTotale = distanceTotaleViaTransit;
                meilleureZoneTransit = tz;
            }
        }
    }

    // Étape 3 : Si aucune zone de transit disponible, trajet direct
    if (meilleureZoneTransit == null) {
        return false;
    }

    // Étape 4 : Comparer avec une marge de tolérance de 30%
    double seuilAcceptable = distanceDirecte * 1.3;
    boolean utilisationTransitAvantageuse = meilleurDistanceTotale <= seuilAcceptable;

    // Étape 5 : Enregistrer la décision et retourner le résultat
    if (utilisationTransitAvantageuse) {
        LogManager.getInstance().logTransit(getName(), "Décision",
            "utilisation de la zone de transit recommandée (efficace)");
    } else {
        LogManager.getInstance().logTransit(getName(), "Décision",
            "livraison directe recommandée (plus courte)");
    }

    return utilisationTransitAvantageuse;
}
```

ANALYSE DÉTAILLÉE DE L'ALGORITHME :

1. CALCUL DU TRAJET DIRECT
   distanceDirecte = distance(positionActuelle, destination)

2. RECHERCHE D'UNE ZONE DE TRANSIT DISPONIBLE
   Pour chaque zone de transit non pleine :
   - Calculer distance(positionActuelle, zoneTransit)
   - Calculer distance(zoneTransit, destination)
   - distanceTotaleViaTransit = distance1 + distance2

3. COMPARAISON AVEC MARGE DE TOLÉRANCE
   seuilAcceptable = distanceDirecte × 1.3 (marge de 30%)

   Si distanceTotaleViaTransit ≤ seuilAcceptable :
      → Utiliser la zone de transit
   Sinon :
      → Trajet direct

Cette marge de 30% permet d'accepter un léger détour en échange des bénéfices
organisationnels des zones de transit (réduction de la congestion, meilleure
répartition des flux).

8.4 Exemple concret de navigation

Suivons Robot1 qui doit aller de [5,10] vers [25,30] :

ÉTAPE 1 : Évaluation des options
- Distance directe : sqrt((25-5)² + (30-10)²) = 28.28 cases
- Zone de transit disponible en [15,20]
- Distance via transit : sqrt((15-5)² + (20-10)²) + sqrt((25-15)² + (30-20)²)
                       = 14.14 + 14.14 = 28.28 cases
- Seuil acceptable : 28.28 × 1.3 = 36.76 cases

ÉTAPE 2 : Décision
Comme 28.28 ≤ 36.76, Robot1 décide d'utiliser la zone de transit.

ÉTAPE 3 : Navigation vers la zone de transit
Robot1 utilise moveOneStepTo(15, 20) :
- Position actuelle : [5,10]
- Directions possibles : [4,10], [6,10], [5,9], [5,11]
- Distances vers [15,20] : 18.03, 18.03, 18.25, 17.80
- Meilleure direction : [5,11] (vers l'est)
- Robot1 se déplace vers [5,11]

ÉTAPE 4 : Continuation du trajet
Le processus se répète à chaque étape jusqu'à atteindre [15,20].

9. SYSTÈME DE COMMUNICATION INTER-ROBOTS

La communication est le système nerveux de notre architecture décentralisée.
Sans elle, les robots ne pourraient pas coordonner leurs efforts.

9.1 Architecture de communication

Le système utilise un modèle de communication par messages asynchrones :

DIFFUSION (Broadcast) : Un robot envoie un message à tous les autres robots
Utilisé pour :
- Annoncer de nouvelles tâches (NewTaskMessage)
- Partager des informations sur les zones de transit
- Diffuser des mises à jour de performance

COMMUNICATION DIRECTE : Un robot envoie un message à un robot spécifique
Utilisé pour :
- Répondre à des offres d'enchères
- Confirmer l'attribution d'une tâche
- Négocier des priorités de passage

9.2 Types de messages détaillés

NewTaskMessage - Annonce de nouvelle tâche
Cette classe est définie dans `Messages.java` et hérite de `RobotMessage`.

Voici l'implémentation de ce message crucial :

```java
/**
 * MESSAGE POUR ANNONCER UNE NOUVELLE TÂCHE À TOUS LES ROBOTS
 * Ce message est diffusé quand un robot découvre un nouveau colis à livrer.
 */
public class NewTaskMessage extends RobotMessage {
    private Task tache;

    public NewTaskMessage(MyTransitRobot expediteur, Task tache) {
        super(expediteur);
        this.tache = tache;
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     * Cette méthode transmet la nouvelle tâche au coordinateur de tâches
     * qui décidera si le robot doit faire une offre.
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Transmettre la nouvelle tâche au coordinateur pour évaluation
        destinataire.getCoordinateurTaches().handleNewTask(tache);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Nouvelle tâche reçue : " + tache.getId());
    }
}
```

BidMessage - Offre d'enchère
Cette classe transporte les offres d'enchères entre robots.

```java
/**
 * MESSAGE POUR TRANSMETTRE UNE OFFRE D'ENCHÈRE
 * Ce message contient l'offre d'un robot pour une tâche spécifique.
 */
public class BidMessage extends RobotMessage {
    private Enchere offre;

    public BidMessage(MyTransitRobot expediteur, Enchere offre) {
        super(expediteur);
        this.offre = offre;
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     * Cette méthode transmet l'offre au coordinateur de tâches du
     * robot receveur pour qu'il puisse l'évaluer.
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        // Transmettre l'offre au coordinateur de tâches pour évaluation
        destinataire.getCoordinateurTaches().handleBid(offre);

        // Enregistrer la réception dans les logs
        LogManager.getInstance().logCoordination(destinataire.getName(),
            "Offre reçue de " + offre.getRobotId() + " pour la tâche " + offre.getTaskId());
    }
}
```

Et voici comment les messages sont diffusés à tous les robots :

```java
/**
 * MÉTHODE POUR DIFFUSER UN MESSAGE À TOUS LES AUTRES ROBOTS
 * Cette méthode envoie un message à tous les robots de l'environnement.
 */
public void broadcastMessage(Message message) {
    // Étape 1 : Récupérer tous les robots de l'environnement
    List<Robot> tousLesRobots = environnement.getRobot();

    // Compteur pour suivre combien de robots ont reçu le message
    int robotsContactes = 0;

    // Étape 2 : Parcourir chaque robot dans l'environnement
    for (Robot robot : tousLesRobots) {
        // Étape 3 : Vérifier que c'est un autre MyTransitRobot (pas nous-même)
        if (robot != this && robot instanceof MyTransitRobot) {
            // Étape 4 : Envoyer le message à ce robot
            ((MyTransitRobot)robot).handleMessage(message);
            robotsContactes++;
        }
    }

    // Étape 5 : Enregistrer la diffusion dans les logs
    LogManager.getInstance().logCoordination(getName(),
        "Message diffusé à " + robotsContactes + " robots");
}
```

CONTENU DES MESSAGES :

NewTaskMessage :
- Identifiant de la tâche (ex: "Task-Colis47")
- Position de départ [x, y]
- Destination [x, y]
- Priorité (1-5)
- Timestamp de création

BidMessage :
- Identifiant de la tâche visée
- Identifiant du robot enchérisseur
- Score d'utilité calculé
- Timestamp de l'offre

TaskCompletionMessage :
- Identifiant de la tâche terminée
- Identifiant du robot qui l'a terminée
- Temps de livraison (en millisecondes)
- Batterie consommée
- Timestamp de fin

9.3 Exemple de séquence de communication complète

Voici un exemple détaillé d'une séquence de communication pour une tâche :

T=0 : Robot3 découvre un colis
Robot3 → BROADCAST : NewTaskMessage("Task-Colis47", [10,15], [45,67], priorité=3)

T=1 : Réception par tous les robots
Robot1 reçoit → Calcule utilité = 0.73 → Génère offre
Robot2 reçoit → Calcule utilité = 0.31 → Pas d'offre (trop faible)
Robot4 reçoit → Calcule utilité = 0.91 → Génère offre
Robot5 reçoit → Calcule utilité = 0.68 → Génère offre

T=2 : Envoi des offres
Robot1 → BROADCAST : BidMessage("Task-Colis47", "Robot1", 0.73)
Robot4 → BROADCAST : BidMessage("Task-Colis47", "Robot4", 0.91)
Robot5 → BROADCAST : BidMessage("Task-Colis47", "Robot5", 0.68)

T=3-4 : Délai d'enchères
Tous les robots collectent les offres et attendent la fin du délai.

T=5 : Attribution automatique
Tous les robots déterminent que Robot4 a la meilleure offre (0.91).
Robot4 commence l'exécution de la tâche.

T=47 : Fin de tâche
Robot4 → BROADCAST : TaskCompletionMessage("Task-Colis47", "Robot4", 42000ms, 15%)

Cette séquence montre comment la coordination émergente se fait sans contrôleur central.

================================================================================
                    PARTIE 4 - INFRASTRUCTURE ET CONFIGURATION
================================================================================

10. COMPOSANTS STATIQUES ET PROTECTION

L'entrepôt contient des éléments fixes qui forment l'infrastructure de base.
Ces composants ne doivent JAMAIS bouger car cela casserait complètement la
logique de navigation des robots.

10.1 Types de composants statiques

StaticStartZone - Zones de départ
Rôle : Points d'apparition des nouveaux colis à livrer
Caractéristiques :
- Position fixe définie dans environment.ini
- Capacité de stockage limitée (généralement 3-5 colis)
- Interface pour que les robots puissent prendre des colis
- Notification automatique quand un nouveau colis arrive

StaticTransitZone - Zones de transit
Rôle : Points de relais pour optimiser les livraisons
Caractéristiques :
- Positions stratégiques dans l'entrepôt
- Capacité de stockage temporaire
- Gestion automatique des colis en attente
- Statistiques d'utilisation pour optimisation

StaticExitZone - Zones de destination
Rôle : Points de livraison finale des colis
Caractéristiques :
- Positions correspondant aux destinations des colis
- Validation automatique des livraisons
- Comptage des colis livrés avec succès

StaticObstacle - Obstacles et murs
Rôle : Éléments bloquants qui forcent les robots à contourner
Caractéristiques :
- Positions fixes qui ne peuvent pas être traversées
- Utilisés pour créer des couloirs et des zones spécialisées

10.2 Système de protection StaticComponentManager

Le StaticComponentManager utilise le pattern Singleton pour garantir qu'aucun
composant statique ne puisse être déplacé accidentellement. Cette classe est
définie dans `StaticComponents.java` avec des méthodes clés comme `getInstance()`,
`registerStaticComponent()` et `isStaticComponent()`.

Voici l'implémentation de ce système de protection crucial :

```java
/**
 * GESTIONNAIRE DES COMPOSANTS STATIQUES
 * Cette classe gère tous les composants statiques de la simulation et s'assure
 * qu'ils ne sont jamais déplacés pendant l'exécution.
 */
public class StaticComponentManager {
    private static StaticComponentManager instance;
    private List<SituatedComponent> composantsStatiques;

    private StaticComponentManager() {
        composantsStatiques = new ArrayList<>();
    }

    /**
     * MÉTHODE POUR OBTENIR L'INSTANCE UNIQUE (PATTERN SINGLETON)
     */
    public static synchronized StaticComponentManager getInstance() {
        if (instance == null) {
            instance = new StaticComponentManager();
        }
        return instance;
    }

    /**
     * MÉTHODE POUR ENREGISTRER UN COMPOSANT STATIQUE
     * Cette méthode ajoute un composant à la liste des composants statiques.
     */
    public void registerStaticComponent(SituatedComponent composant) {
        // Ajouter le composant à la liste des composants protégés
        composantsStatiques.add(composant);

        // Enregistrer cette action dans les logs
        LogManager.getInstance().logAction("StaticComponentManager",
            "Composant statique enregistré à la position [" +
            composant.getLocation()[0] + "," + composant.getLocation()[1] + "]");
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI UN COMPOSANT EST STATIQUE
     */
    public boolean isStaticComponent(SituatedComponent composant) {
        return composantsStatiques.contains(composant);
    }
}
```

Et voici comment les zones statiques se protègent contre les déplacements :

```java
/**
 * CLASSE ABSTRAITE POUR LES ZONES STATIQUES
 * Cette classe surcharge les méthodes de déplacement pour les rendre inopérantes.
 */
abstract class ZoneStatique extends SituatedComponent {

    /**
     * MÉTHODE SURCHARGÉE POUR EMPÊCHER TOUT DÉPLACEMENT
     * Cette méthode ne fait rien, même si du code essaie de déplacer la zone.
     */
    @Override
    public void setLocation(int x, int y) {
        // Étape 1 : Enregistrer la tentative de déplacement comme une erreur
        LogManager.getInstance().logError(getClass().getSimpleName(),
            "Tentative de déplacement d'un composant statique détectée et bloquée");

        // Étape 2 : Ne rien faire - la position reste inchangée
        // Cette approche garantit que la zone reste à sa position originale
    }

    /**
     * CONSTRUCTEUR PROTÉGÉ POUR LES ZONES STATIQUES
     * Enregistre automatiquement la zone dans le gestionnaire de protection.
     */
    protected ZoneStatique(int x, int y, Color couleur) {
        super(x, y, couleur);

        // Enregistrer cette zone comme composant statique protégé
        StaticComponentManager.getInstance().registerStaticComponent(this);
    }
}
```

Mécanisme de protection :

1. ENREGISTREMENT À LA CRÉATION
   Quand un composant statique est créé, il est automatiquement enregistré
   via `registerStaticComponent(SituatedComponent composant)` dans la liste
   des composants protégés.

2. SURCHARGE DES MÉTHODES DE DÉPLACEMENT
   Tous les composants statiques (StaticStartZone, StaticTransitZone, etc.)
   héritent de la classe abstraite `ZoneStatique` qui surcharge `setLocation()`
   pour qu'elle ne fasse rien, même si du code essaie de les déplacer.

3. DÉTECTION DES VIOLATIONS
   Si du code tente de déplacer un composant statique, le gestionnaire :
   - Enregistre une erreur via `LogManager.getInstance().logError()`
   - Annule le déplacement
   - Maintient le composant à sa position originale

Cette protection est cruciale car les robots mémorisent les positions des zones
et des obstacles. Si ces éléments bougeaient, les robots seraient complètement
perdus.

11. SYSTÈME DE JOURNALISATION AVANCÉ

Le LogManager est bien plus qu'un simple système de logs. C'est un outil
d'analyse et de debug sophistiqué qui permet de comprendre exactement ce
qui se passe dans la simulation. Cette classe utilise le pattern Singleton
avec la méthode `getInstance()` et propose des méthodes spécialisées comme
`logAction()`, `logBattery()`, `logTransit()`, `logCoordination()`,
`logDelivery()` et `logError()`.

Voici l'implémentation du système de journalisation :

```java
/**
 * GESTIONNAIRE CENTRALISÉ DE JOURNALISATION
 * Cette classe utilise le pattern Singleton pour centraliser tous les logs
 * du système avec catégorisation et couleurs.
 */
public class LogManager {
    private static LogManager instance;
    private Map<TypeDeMessage, AtomicInteger> statistiquesGlobales;
    private Map<String, Map<TypeDeMessage, AtomicInteger>> statistiquesParRobot;

    // Codes couleur ANSI pour l'affichage
    private static final String ANSI_GREEN = "\u001B[32m";
    private static final String ANSI_YELLOW = "\u001B[33m";
    private static final String ANSI_BLUE = "\u001B[34m";
    private static final String ANSI_PURPLE = "\u001B[35m";
    private static final String ANSI_CYAN = "\u001B[36m";
    private static final String ANSI_RED = "\u001B[31m";
    private static final String ANSI_RESET = "\u001B[0m";

    /**
     * MÉTHODE POUR OBTENIR L'INSTANCE UNIQUE (PATTERN SINGLETON)
     */
    public static synchronized LogManager getInstance() {
        if (instance == null) {
            instance = new LogManager();
        }
        return instance;
    }

    /**
     * MÉTHODE POUR ENREGISTRER LES ÉVÉNEMENTS LIÉS À LA BATTERIE
     * Cette méthode formate automatiquement le niveau de batterie en pourcentage.
     */
    public void logBattery(String nomRobot, double niveauBatterie, String messageDetail) {
        String messageComplet = String.format("Niveau: %.1f%% - %s", niveauBatterie, messageDetail);
        enregistrerMessage(nomRobot, TypeDeMessage.BATTERY, messageComplet);
    }

    /**
     * MÉTHODE POUR ENREGISTRER LES ÉVÉNEMENTS DE COORDINATION
     * Cette méthode est utilisée pour tous les messages entre robots.
     */
    public void logCoordination(String nomRobot, String messageCoordination) {
        enregistrerMessage(nomRobot, TypeDeMessage.COORDINATION, messageCoordination);
    }

    /**
     * MÉTHODE PRIVÉE POUR ENREGISTRER UN MESSAGE AVEC COULEUR ET TIMESTAMP
     */
    private void enregistrerMessage(String nomRobot, TypeDeMessage type, String message) {
        // Générer le timestamp
        String timestamp = getCurrentTimestamp();

        // Choisir la couleur selon le type
        String couleur = getCouleurPourType(type);
        String nomType = type.toString();

        // Afficher le message formaté
        System.out.println(couleur + "[" + nomType + "] " + timestamp + " - " +
                          nomRobot + ": " + message + ANSI_RESET);

        // Mettre à jour les statistiques
        mettreAJourStatistiques(nomRobot, type);
    }
}
```

11.1 Catégorisation des messages

Chaque message est catégorisé par type avec une couleur spécifique :

ACTION (VERT) - Actions normales des robots
Exemples :
- "Robot2 se déplace vers [15,20]"
- "Robot4 a pris un colis dans la zone [10,15]"
- "Robot1 change d'orientation vers le nord"

BATTERY (JAUNE) - Événements liés à la batterie
Exemples :
- "Robot3 : Charge 85% → 90% - charge en cours"
- "Robot1 : Mouvement -1% (95% → 94%)"
- "Robot5 : niveau de batterie critique, recherche station de charge"

TRANSIT (BLEU) - Mouvements entre zones spécifiques
Exemples :
- "Robot2 : Zone [20,20] - arrivé à la zone de transit"
- "Robot4 : Calcul trajet - utilisation zone de transit recommandée"
- "Robot1 : Zone [45,67] - livraison terminée avec succès"

COORDINATION (CYAN) - Communications entre robots
Exemples :
- "Robot3 : Nouvelle tâche créée Task-Colis47"
- "Robot1 : Offre envoyée pour Task-Colis47 avec score 0.73"
- "Robot4 : Tâche Task-Colis47 attribuée avec succès"

DELIVERY (VIOLET) - Livraisons et métriques de performance
Exemples :
- "Robot2 : Livraison terminée en 42.5s avec 15% de batterie"
- "Robot4 : Score d'efficacité mis à jour : 0.92"
- "Système : 47 colis livrés au total"

ERROR (ROUGE) - Erreurs et situations exceptionnelles
Exemples :
- "Robot1 : Tentative de déplacement d'un composant statique détectée"
- "Robot3 : Aucune station de charge accessible avec batterie critique"
- "Système : Conflit d'attribution détecté pour Task-Colis52"

11.2 Statistiques automatiques

Le LogManager collecte automatiquement des statistiques détaillées via des
structures de données internes (`Map<TypeDeMessage, AtomicInteger> statistiquesGlobales`
et `Map<String, Map<TypeDeMessage, AtomicInteger>> statistiquesParRobot`).

STATISTIQUES GLOBALES :
- Nombre total de messages par catégorie
- Taux d'erreurs du système
- Fréquence des communications
- Performance globale du système

STATISTIQUES PAR ROBOT :
- Nombre d'actions effectuées
- Événements de batterie
- Messages de coordination envoyés/reçus
- Taux de succès des enchères

Ces statistiques sont accessibles via les méthodes `getGlobalStatistics()` et
`getRobotStatistics(String robotName)` et permettent d'identifier les goulots
d'étranglement pour optimiser les paramètres du système.

De plus, le CoordinateurTaches maintient ses propres métriques de performance
via la méthode `updateRobotEfficiency(MyTransitRobot robot, long tempsDeLivraison,
double batterieUtilisee)` et compile les statistiques avec `getStatistics()`.

12. CONFIGURATION ET PARAMÈTRES

Le système utilise plusieurs fichiers de configuration pour s'adapter à
différents scénarios d'utilisation.

12.1 Fichier configuration.ini

Paramètres de simulation :
- nbrobot = 15 (nombre de robots dans la simulation)
- rows = 50, columns = 50 (taille de la grille)
- field = 3 (champ de vision des robots)
- seed = 12345 (graine pour la reproductibilité)

Paramètres d'affichage :
- debug = 1 (niveau de détail des logs)
- colorrobot = RGB(0,255,0) (couleur des robots)
- vitesse = 100ms (délai entre les étapes)

12.2 Fichier environment.ini

Positions des zones de départ :
startZonePositions = [5,5],[15,5],[25,5],[35,5],[45,5]

Zones de transit stratégiques :
transitZoneData = [15,15],[25,25],[35,35]

Destinations possibles :
goalPositions = [10,45],[20,45],[30,45],[40,45]

Obstacles et murs :
obstaclePositions = [12,12],[13,12],[14,12],[15,12]

12.3 Paramètres critiques du système

BATTERIE :
- MAX_BATTERY = 100 (capacité maximale)
- CRITICAL_BATTERY_THRESHOLD = 20 (seuil critique)
- MOVE_BATTERY_COST = 1 (coût par déplacement)
- CHARGE_RATE = 5 (vitesse de charge par étape)

ENCHÈRES :
- BID_TIMEOUT = 2000ms (délai d'attente des offres)
- MIN_UTILITY_THRESHOLD = 0.5 (seuil minimum pour faire une offre)
- POIDS_DISTANCE = 0.4 (importance de la distance dans le calcul d'utilité)
- POIDS_BATTERIE = 0.3 (importance de la batterie)

NAVIGATION :
- TRANSIT_TOLERANCE = 1.3 (marge acceptable pour utiliser une zone de transit)
- COLLISION_WAIT_TIME = 3 (étapes d'attente en cas de blocage)

13. MÉTRIQUES DE PERFORMANCE ET ANALYSE

Le système collecte automatiquement de nombreuses métriques qui permettent
d'évaluer son efficacité.

13.1 Métriques de livraison

Temps de livraison moyen :
Calculé depuis la prise en charge du colis jusqu'à sa livraison finale.
Objectif : < 60 secondes pour un entrepôt 50x50

Taux de succès des livraisons :
Pourcentage de colis livrés avec succès par rapport au nombre total généré.
Objectif : > 95%

Efficacité énergétique :
Batterie moyenne consommée par livraison.
Objectif : < 20% par livraison

13.2 Métriques de coordination

Taux de succès aux enchères :
Pourcentage d'offres gagnantes par robot.
Permet d'identifier les robots les plus efficaces.

Temps de négociation moyen :
Délai entre l'annonce d'une tâche et son attribution.
Objectif : < 5 secondes

Équilibrage de charge :
Écart-type du nombre de tâches par robot.
Un faible écart-type indique une bonne répartition.

13.3 Métriques système

Utilisation des zones de transit :
Pourcentage de livraisons utilisant les zones de transit.
Permet d'optimiser le placement des zones.

Congestion aux stations de charge :
Nombre moyen de robots en attente de charge.
Indique si plus de stations sont nécessaires.

Débit global :
Nombre de colis livrés par minute.
Métrique principale de performance du système.

================================================================================
                                CONCLUSION
================================================================================

14. SYNTHÈSE DU SYSTÈME

Ce système de simulation robotique représente une approche révolutionnaire de
la coordination multi-agents. En abandonnant l'architecture centralisée
traditionnelle au profit d'un système d'enchères décentralisé, nous avons
créé une solution qui est à la fois plus robuste, plus évolutive et plus
réaliste que les approches classiques.

14.1 Innovations principales

DÉCENTRALISATION COMPLÈTE :
Chaque robot prend ses propres décisions grâce à son CoordinateurTaches
personnel. Il n'y a aucun point de défaillance unique dans le système.

SYSTÈME D'ENCHÈRES SOPHISTIQUÉ :
L'algorithme de calcul d'utilité prend en compte 6 facteurs différents
pour optimiser l'attribution des tâches de manière équitable et efficace.

OPTIMISATION INTELLIGENTE DES TRAJETS :
Les robots choisissent automatiquement entre trajets directs et passages
par zones de transit selon un calcul d'efficacité précis.

GESTION ÉNERGÉTIQUE AVANCÉE :
La stratégie "charge critique uniquement" maximise le temps de travail
effectif des robots.

COMMUNICATION ASYNCHRONE :
Le système de messages permet une coordination fluide sans synchronisation
globale contraignante.

14.2 Bénéfices démontrés

ROBUSTESSE :
Le système continue à fonctionner même si des robots tombent en panne.
Les tâches sont automatiquement redistribuées aux robots disponibles.

ÉVOLUTIVITÉ :
On peut ajouter ou retirer des robots sans reconfiguration. Le système
s'adapte automatiquement à la nouvelle configuration.

EFFICACITÉ :
Les enchères garantissent que chaque tâche est attribuée au robot le
mieux placé pour l'exécuter, optimisant ainsi les performances globales.

RÉALISME :
Le comportement émergent du système reproduit fidèlement les dynamiques
d'un vrai entrepôt automatisé.

14.3 Applications pratiques

Ce système peut être adapté pour de nombreuses applications réelles :

ENTREPÔTS LOGISTIQUES :
Gestion automatisée des commandes e-commerce avec optimisation des flux.

HÔPITAUX :
Transport automatisé de médicaments, échantillons et équipements médicaux.

USINES :
Approvisionnement automatisé des chaînes de production.

AÉROPORTS :
Gestion automatisée des bagages et du fret.

14.4 Perspectives d'évolution

Le système actuel constitue une base solide pour de nombreuses améliorations :

APPRENTISSAGE AUTOMATIQUE :
Intégration d'algorithmes d'apprentissage pour optimiser automatiquement
les paramètres du système selon les conditions d'utilisation.

PRÉDICTION INTELLIGENTE :
Développement de capacités de prédiction pour anticiper l'apparition des
colis et positionner les robots de manière proactive.

ROBOTS HÉTÉROGÈNES :
Extension pour gérer des flottes de robots avec différentes capacités
(vitesse, autonomie, capacité de charge).

OPTIMISATION MULTI-OBJECTIFS :
Prise en compte d'objectifs supplémentaires comme la minimisation de
l'usure des robots ou la prioritisation de certains types de colis.

Ce projet démontre qu'il est possible de créer des systèmes robotiques
complexes et efficaces sans recourir à une architecture centralisée.
L'approche décentralisée par enchères ouvre la voie à une nouvelle
génération de systèmes robotiques plus intelligents, plus robustes
et plus adaptables aux défis de l'industrie moderne.

La documentation exhaustive et le code commenté en français font de ce
projet un excellent exemple pédagogique pour comprendre les principes
de la robotique multi-agents et de la coordination décentralisée.
